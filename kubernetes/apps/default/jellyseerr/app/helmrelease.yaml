---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: jellyseerr
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      jellyseerr:
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: ghcr.io/fallenbagel/jellyseerr
              tag: 2.7.3@sha256:9cc9e9ee6cd5cf5a23feb45c37742ba34cfd6314d81d259cddb373a97ac92cdd
            env:
              TZ: America/New_York
              LOG_LEVEL: "info"
              PORT: &port 80
            envFrom:
              - secretRef:
                  name: jellyseerr-secret
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /api/v1/status
                    port: *port
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 20m
                memory: 512Mi
              limits:
                memory: 1Gi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    service:
      app:
        ports:
          http:
            port: *port
    route:
      app:
        hostnames:
          - requests.${SECRET_DOMAIN}
          - "{{ .Release.Name }}.${SECRET_DOMAIN}"
        parentRefs:
          - name: jellyseerr-gateway
            namespace: default
            sectionName: https
          - name: jellyseerr-gateway
            namespace: default
            sectionName: https-jellyseerr
    persistence:
      config:
        existingClaim: jellyseerr
        globalMounts:
          - path: /app/config
      config-cache:
        existingClaim: jellyseerr-cache
        globalMounts:
          - path: /app/config/cache
      tmpfs:
        type: emptyDir
        advancedMounts:
          jellyseerr:
            app:
              - path: /app/config/logs
                subPath: logs
              - path: /tmp
                subPath: tmp
