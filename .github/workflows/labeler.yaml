---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: Labeler

on:
  workflow_dispatch:
  pull_request:
    branches: ["main"]

permissions:
  contents: read

jobs:
  main:
    name: Labeler - Labeler
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository }}
    permissions:
      contents: read
      pull-requests: write
    steps:
      - name: Generate Token
        uses: actions/create-github-app-token@a8d616148505b5069dccd32f177bb87d7f39123b # v2.1.1
        id: app-token
        with:
          app-id: "${{ secrets.BOT_APP_ID }}"
          private-key: "${{ secrets.BOT_APP_PRIVATE_KEY }}"

      - name: Labeler
        uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9 # v5.0.0
        with:
          repo-token: "${{ steps.app-token.outputs.token }}"
          configuration-path: .github/labeler.yaml
