---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: home-ops-runner
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: home-ops-runner-secret
    template:
      data:
        github_app_id: '{{ .ACTIONS_RUNNER_APP_ID }}'
        github_app_installation_id: '{{ .ACTIONS_RUNNER_INSTALLATION_ID }}'
        github_app_private_key: '{{ .ACTIONS_RUNNER_PRIVATE_KEY }}'
  dataFrom:
    - extract:
        key: actions-runner
