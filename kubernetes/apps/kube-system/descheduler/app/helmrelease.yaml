---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: descheduler
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 0.33.0
  url: oci://ghcr.io/home-operations/charts-mirror/descheduler
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: descheduler
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: descheduler
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    kind: Deployment
    deschedulerPolicyAPIVersion: descheduler/v1alpha2
    deschedulerPolicy:
      profiles:
        - name: Default
          pluginConfig:
            - name: DefaultEvictor
              args:
                evictFailedBarePods: true
                evictLocalStoragePods: true
                evictSystemCriticalPods: true
            - name: RemoveFailedPods
              args:
                reasons:
                  - ContainerStatusUnknown
                  - NodeAffinity
                  - NodeShutdown
                  - Terminated
                  - UnexpectedAdmissionError
                includingInitContainers: true
                excludeOwnerKinds:
                  - Job
                minPodLifetimeSeconds: 1800
            - name: RemovePodsViolatingInterPodAntiAffinity
            - name: RemovePodsViolatingNodeAffinity
              args:
                nodeAffinityType:
                  - requiredDuringSchedulingIgnoredDuringExecution
            - name: RemovePodsViolatingNodeTaints
            - name: RemovePodsViolatingTopologySpreadConstraint
          plugins:
            balance:
              enabled:
                - RemovePodsViolatingTopologySpreadConstraint
            deschedule:
              enabled:
                - RemoveFailedPods
                - RemovePodsViolatingInterPodAntiAffinity
                - RemovePodsViolatingNodeAffinity
                - RemovePodsViolatingNodeTaints
    service:
      enabled: true
    serviceMonitor:
      enabled: true
    leaderElection:
      enabled: true
