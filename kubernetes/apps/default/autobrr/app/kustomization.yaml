---
# yaml-language-server: $schema=https://json.schemastore.org/kustomization
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ./externalsecret.yaml
  - ./helmrelease.yaml
  - ./prometheusrule.yaml
configMapGenerator:
  - name: autobrr-loki-rules
    files:
      - autobrr.yaml=./lokirule.yaml
    options:
      labels:
        loki_rule: "true"
generatorOptions:
  disableNameSuffixHash: true
  annotations:
    kustomize.toolkit.fluxcd.io/substitute: disabled
