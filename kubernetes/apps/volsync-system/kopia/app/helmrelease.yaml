---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: kopia
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      kopia:
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: ghcr.io/home-operations/kopia
              tag: 0.21.1@sha256:f666b5f2c1ea4649cd2bd703507d4b81c2b515782e8476ba4a145b091a704a53
            env:
              KOPIA_WEB_ENABLED: true
              KOPIA_WEB_PORT: &port 80
              TZ: America/New_York
            envFrom:
              - secretRef:
                  name: kopia-secret
            args:
              - --without-password
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /
                    port: *port
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 10m
              limits:
                memory: 1Gi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    service:
      app:
        ports:
          http:
            port: *port
    route:
      app:
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: kopia-gateway
            namespace: volsync-system
            sectionName: https
    persistence:
      config-file:
        type: configMap
        name: kopia-repository-configmap
        globalMounts:
          - path: /config/repository.config
            subPath: repository.config
            readOnly: true
      repository:
        type: nfs
        server: **************
        path: /mnt/flashstor/VolsyncKopia
        globalMounts:
          - path: /repository
      tmpfs:
        type: emptyDir
        advancedMounts:
          kopia:
            app:
              - path: /config/cache
                subPath: cache
              - path: /config/logs
                subPath: logs
