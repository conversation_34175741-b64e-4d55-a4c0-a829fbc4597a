# TrueNAS VM API Deep Reference

*Generated by deep API exploration with real API calls*

**Generated on:** 2025-08-10 03:34:30

## Table of Contents

1. [Real VM Examples](#real-vm-examples)
2. [Real Dataset Examples](#real-dataset-examples)
3. [API Method Details](#api-method-details)
4. [Device Structure Analysis](#device-structure-analysis)

## Real VM Examples

These are actual VMs from the TrueNAS system:

### VM Example 1

```json
{
  "arch_type": null,
  "autostart": true,
  "bootloader": "UEFI",
  "bootloader_ovmf": "OVMF_CODE.fd",
  "command_line_args": "",
  "cores": 1,
  "cpu_mode": "HOST-PASSTHROUGH",
  "cpu_model": null,
  "cpuset": "",
  "description": "Talos Linux VM - k8s_0",
  "devices": [
    {
      "attributes": {
        "dtype": "CDROM",
        "path": "/mnt/flashstor/ISO/metal-amd64.iso"
      },
      "id": 98,
      "order": 1010,
      "vm": 20
    },
    {
      "attributes": {
        "dtype": "NIC",
        "mac": "00:a0:98:28:c8:83",
        "nic_attach": "br0",
        "trust_guest_rx_filters": false,
        "type": "VIRTIO"
      },
      "id": 99,
      "order": 1002,
      "vm": 20
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_0-boot",
        "physical_sectorsize": null,
        "serial": "XyHh8zYv",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 100,
      "order": 1001,
      "vm": 20
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_0-ebs",
        "physical_sectorsize": null,
        "serial": "50GDMjhJ",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 101,
      "order": 1004,
      "vm": 20
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_0-rook",
        "physical_sectorsize": null,
        "serial": "WNOgp5Vu",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 102,
      "order": 1005,
      "vm": 20
    },
    {
      "attributes": {
        "bind": "192.168.120.10",
        "dtype": "DISPLAY",
        "password": "AXwGox74LMkszJCA",
        "port": 5900,
        "resolution": "1920x1080",
        "type": "SPICE",
        "wait": false,
        "web": true,
        "web_port": 5901
      },
      "id": 103,
      "order": 1003,
      "vm": 20
    }
  ],
  "display_available": true,
  "enable_cpu_topology_extension": false,
  "enable_secure_boot": false,
  "ensure_display_device": true,
  "hide_from_msr": false,
  "hyperv_enlightenments": false,
  "id": 20,
  "machine_type": null,
  "memory": 32768,
  "min_memory": null,
  "name": "k8s_0",
  "nodeset": "",
  "pin_vcpus": false,
  "shutdown_timeout": 90,
  "status": {
    "domain_state": "RUNNING",
    "pid": 11711,
    "state": "RUNNING"
  },
  "suspend_on_snapshot": false,
  "threads": 1,
  "time": "LOCAL",
  "trusted_platform_module": false,
  "uuid": "e1f16e03-a75e-4a23-8595-9c1676e90bed",
  "vcpus": 8
}
```

### VM Example 2

```json
{
  "arch_type": null,
  "autostart": true,
  "bootloader": "UEFI",
  "bootloader_ovmf": "OVMF_CODE.fd",
  "command_line_args": "",
  "cores": 1,
  "cpu_mode": "HOST-PASSTHROUGH",
  "cpu_model": null,
  "cpuset": "",
  "description": "Talos Linux VM - k8s_1",
  "devices": [
    {
      "attributes": {
        "dtype": "CDROM",
        "path": "/mnt/flashstor/ISO/metal-amd64.iso"
      },
      "id": 104,
      "order": 1010,
      "vm": 21
    },
    {
      "attributes": {
        "dtype": "NIC",
        "mac": "00:a0:98:1a:f3:72",
        "nic_attach": "br0",
        "trust_guest_rx_filters": false,
        "type": "VIRTIO"
      },
      "id": 105,
      "order": 1002,
      "vm": 21
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_1-boot",
        "physical_sectorsize": null,
        "serial": "ZlPEGSnr",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 106,
      "order": 1001,
      "vm": 21
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_1-ebs",
        "physical_sectorsize": null,
        "serial": "OBUP4RxD",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 107,
      "order": 1004,
      "vm": 21
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_1-rook",
        "physical_sectorsize": null,
        "serial": "m4jfGrnB",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 108,
      "order": 1005,
      "vm": 21
    },
    {
      "attributes": {
        "bind": "192.168.120.10",
        "dtype": "DISPLAY",
        "password": "AXwGox74LMkszJCA",
        "port": 5902,
        "resolution": "1920x1080",
        "type": "SPICE",
        "wait": false,
        "web": true,
        "web_port": 5903
      },
      "id": 109,
      "order": 1003,
      "vm": 21
    }
  ],
  "display_available": true,
  "enable_cpu_topology_extension": false,
  "enable_secure_boot": false,
  "ensure_display_device": true,
  "hide_from_msr": false,
  "hyperv_enlightenments": false,
  "id": 21,
  "machine_type": null,
  "memory": 32768,
  "min_memory": null,
  "name": "k8s_1",
  "nodeset": "",
  "pin_vcpus": false,
  "shutdown_timeout": 90,
  "status": {
    "domain_state": "RUNNING",
    "pid": 11881,
    "state": "RUNNING"
  },
  "suspend_on_snapshot": false,
  "threads": 1,
  "time": "LOCAL",
  "trusted_platform_module": false,
  "uuid": "bc598420-f141-43d4-af95-88f61989304a",
  "vcpus": 8
}
```

### VM Example 3

```json
{
  "arch_type": null,
  "autostart": true,
  "bootloader": "UEFI",
  "bootloader_ovmf": "OVMF_CODE.fd",
  "command_line_args": "",
  "cores": 1,
  "cpu_mode": "HOST-PASSTHROUGH",
  "cpu_model": null,
  "cpuset": "",
  "description": "Talos Linux VM - k8s_2",
  "devices": [
    {
      "attributes": {
        "dtype": "CDROM",
        "path": "/mnt/flashstor/ISO/metal-amd64.iso"
      },
      "id": 110,
      "order": 1010,
      "vm": 22
    },
    {
      "attributes": {
        "dtype": "NIC",
        "mac": "00:a0:98:3e:6c:22",
        "nic_attach": "br0",
        "trust_guest_rx_filters": false,
        "type": "VIRTIO"
      },
      "id": 111,
      "order": 1002,
      "vm": 22
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_2-boot",
        "physical_sectorsize": null,
        "serial": "31GAWUaG",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 112,
      "order": 1001,
      "vm": 22
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_2-ebs",
        "physical_sectorsize": null,
        "serial": "adBVncSX",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 113,
      "order": 1004,
      "vm": 22
    },
    {
      "attributes": {
        "create_zvol": false,
        "dtype": "DISK",
        "iotype": "THREADS",
        "logical_sectorsize": null,
        "path": "/dev/zvol/flashstor/VM/k8s_2-rook",
        "physical_sectorsize": null,
        "serial": "WCVdzBbR",
        "type": "VIRTIO",
        "zvol_name": null,
        "zvol_volsize": null
      },
      "id": 114,
      "order": 1005,
      "vm": 22
    },
    {
      "attributes": {
        "bind": "192.168.120.10",
        "dtype": "DISPLAY",
        "password": "AXwGox74LMkszJCA",
        "port": 5904,
        "resolution": "1920x1080",
        "type": "SPICE",
        "wait": false,
        "web": true,
        "web_port": 5905
      },
      "id": 115,
      "order": 1003,
      "vm": 22
    }
  ],
  "display_available": true,
  "enable_cpu_topology_extension": false,
  "enable_secure_boot": false,
  "ensure_display_device": true,
  "hide_from_msr": false,
  "hyperv_enlightenments": false,
  "id": 22,
  "machine_type": null,
  "memory": 32768,
  "min_memory": null,
  "name": "k8s_2",
  "nodeset": "",
  "pin_vcpus": false,
  "shutdown_timeout": 90,
  "status": {
    "domain_state": "RUNNING",
    "pid": 12039,
    "state": "RUNNING"
  },
  "suspend_on_snapshot": false,
  "threads": 1,
  "time": "LOCAL",
  "trusted_platform_module": false,
  "uuid": "66993652-eee7-4ea6-86c0-e74f33227f30",
  "vcpus": 8
}
```

## Real Dataset Examples

These are actual datasets from the TrueNAS system:

### Dataset Example 1

```json
{
  "aclmode": {
    "parsed": "discard",
    "rawvalue": "discard",
    "source": "DEFAULT",
    "source_info": null,
    "value": "DISCARD"
  },
  "acltype": {
    "parsed": "posix",
    "rawvalue": "posix",
    "source": "LOCAL",
    "source_info": null,
    "value": "POSIX"
  },
  "atime": {
    "parsed": false,
    "rawvalue": "off",
    "source": "LOCAL",
    "source_info": null,
    "value": "OFF"
  },
  "available": {
    "parsed": 16757821094208,
    "rawvalue": "16757821094208",
    "source": "NONE",
    "source_info": null,
    "value": "15.2T"
  },
  "casesensitivity": {
    "parsed": "sensitive",
    "rawvalue": "sensitive",
    "source": "NONE",
    "source_info": null,
    "value": "SENSITIVE"
  },
  "checksum": {
    "parsed": true,
    "rawvalue": "on",
    "source": "LOCAL",
    "source_info": null,
    "value": "ON"
  },
  "children": [
    {
      "aclmode": {
        "parsed": "discard",
        "rawvalue": "discard",
        "source": "DEFAULT",
        "source_info": null,
        "value": "DISCARD"
      },
      "acltype": {
        "parsed": "posix",
        "rawvalue": "posix",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "POSIX"
      },
      "atime": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "available": {
        "parsed": 1024866432,
        "rawvalue": "1024866432",
        "source": "NONE",
        "source_info": null,
        "value": "977M"
      },
      "casesensitivity": {
        "parsed": "sensitive",
        "rawvalue": "sensitive",
        "source": "NONE",
        "source_info": null,
        "value": "SENSITIVE"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "LOCAL",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "2.39",
        "rawvalue": "2.39",
        "source": "NONE",
        "source_info": null,
        "value": "2.39x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "LOCAL",
        "source_info": null,
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1753060422000
        },
        "rawvalue": "1753060422",
        "source": "NONE",
        "source_info": null,
        "value": "Sun Jul 20 21:13 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "exec": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "id": "flashstor/scripts",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": "/mnt/flashstor/scripts",
      "name": "flashstor/scripts",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "quota": {
        "parsed": 1073741824,
        "rawvalue": "1073741824",
        "source": "LOCAL",
        "source_info": null,
        "value": "1G"
      },
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "recordsize": {
        "parsed": 131072,
        "rawvalue": "131072",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "128K"
      },
      "refquota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "snapdir": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "special_small_block_size": {
        "parsed": "32768",
        "rawvalue": "32768",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "32K"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "FILESYSTEM",
      "used": {
        "parsed": 48875392,
        "rawvalue": "48875392",
        "source": "NONE",
        "source_info": null,
        "value": "46.6M"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 48875392,
        "rawvalue": "48875392",
        "source": "NONE",
        "source_info": null,
        "value": "46.6M"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "user_properties": {},
      "xattr": {
        "parsed": true,
        "rawvalue": "on",
        "source": "LOCAL",
        "source_info": null,
        "value": "ON"
      }
    },
    {
      "aclmode": {
        "parsed": "discard",
        "rawvalue": "discard",
        "source": "DEFAULT",
        "source_info": null,
        "value": "DISCARD"
      },
      "acltype": {
        "parsed": "posix",
        "rawvalue": "posix",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "POSIX"
      },
      "atime": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "casesensitivity": {
        "parsed": "sensitive",
        "rawvalue": "sensitive",
        "source": "NONE",
        "source_info": null,
        "value": "SENSITIVE"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.00",
        "rawvalue": "1.00",
        "source": "NONE",
        "source_info": null,
        "value": "1.00x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1720717601000
        },
        "rawvalue": "1720717601",
        "source": "NONE",
        "source_info": null,
        "value": "Thu Jul 11 13:06 2024"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "exec": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "id": "flashstor/data",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": "/mnt/flashstor/data",
      "name": "flashstor/data",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "quota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "recordsize": {
        "parsed": 131072,
        "rawvalue": "131072",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "128K"
      },
      "refquota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "snapdir": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "special_small_block_size": {
        "parsed": "32768",
        "rawvalue": "32768",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "32K"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "FILESYSTEM",
      "used": {
        "parsed": 7764289774016,
        "rawvalue": "7764289774016",
        "source": "NONE",
        "source_info": null,
        "value": "7.06T"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 7761836177408,
        "rawvalue": "7761836177408",
        "source": "NONE",
        "source_info": null,
        "value": "7.06T"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 2453596608,
        "rawvalue": "2453596608",
        "source": "NONE",
        "source_info": null,
        "value": "2.29G"
      },
      "user_properties": {},
      "xattr": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      }
    },
    {
      "aclmode": {
        "parsed": "discard",
        "rawvalue": "discard",
        "source": "DEFAULT",
        "source_info": null,
        "value": "DISCARD"
      },
      "acltype": {
        "parsed": "posix",
        "rawvalue": "posix",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "POSIX"
      },
      "atime": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "casesensitivity": {
        "parsed": "sensitive",
        "rawvalue": "sensitive",
        "source": "NONE",
        "source_info": null,
        "value": "SENSITIVE"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "1.72",
            "rawvalue": "1.72",
            "source": "NONE",
            "source_info": null,
            "value": "1.72x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357217000
            },
            "rawvalue": "1754357217",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:26 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_0-boot",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_0-boot",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 26502875456,
            "rawvalue": "26502875456",
            "source": "NONE",
            "source_info": null,
            "value": "24.7G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 20398826560,
            "rawvalue": "20398826560",
            "source": "NONE",
            "source_info": null,
            "value": "19.0G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 6104048896,
            "rawvalue": "6104048896",
            "source": "NONE",
            "source_info": null,
            "value": "5.68G"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 268435456000,
            "rawvalue": "268435456000",
            "source": "LOCAL",
            "source_info": null,
            "value": "250G"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "1.53",
            "rawvalue": "1.53",
            "source": "NONE",
            "source_info": null,
            "value": "1.53x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357260000
            },
            "rawvalue": "1754357260",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:27 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_1-ebs",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_1-ebs",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 1103523584,
            "rawvalue": "1103523584",
            "source": "NONE",
            "source_info": null,
            "value": "1.03G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 1099011712,
            "rawvalue": "1099011712",
            "source": "NONE",
            "source_info": null,
            "value": "1.02G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 4511872,
            "rawvalue": "4511872",
            "source": "NONE",
            "source_info": null,
            "value": "4.30M"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 1099511627776,
            "rawvalue": "1099511627776",
            "source": "LOCAL",
            "source_info": null,
            "value": "1T"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "2.15",
            "rawvalue": "2.15",
            "source": "NONE",
            "source_info": null,
            "value": "2.15x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357315000
            },
            "rawvalue": "1754357315",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:28 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_2-rook",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_2-rook",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 17887698432,
            "rawvalue": "17887698432",
            "source": "NONE",
            "source_info": null,
            "value": "16.7G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 10771042752,
            "rawvalue": "10771042752",
            "source": "NONE",
            "source_info": null,
            "value": "10.0G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 7116655680,
            "rawvalue": "7116655680",
            "source": "NONE",
            "source_info": null,
            "value": "6.63G"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 858993459200,
            "rawvalue": "858993459200",
            "source": "LOCAL",
            "source_info": null,
            "value": "800G"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "1.72",
            "rawvalue": "1.72",
            "source": "NONE",
            "source_info": null,
            "value": "1.72x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357262000
            },
            "rawvalue": "1754357262",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:27 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_1-boot",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_1-boot",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 28728543232,
            "rawvalue": "28728543232",
            "source": "NONE",
            "source_info": null,
            "value": "26.8G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 22668408832,
            "rawvalue": "22668408832",
            "source": "NONE",
            "source_info": null,
            "value": "21.1G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 6060134400,
            "rawvalue": "6060134400",
            "source": "NONE",
            "source_info": null,
            "value": "5.64G"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 268435456000,
            "rawvalue": "268435456000",
            "source": "LOCAL",
            "source_info": null,
            "value": "250G"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "1.60",
            "rawvalue": "1.60",
            "source": "NONE",
            "source_info": null,
            "value": "1.60x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357218000
            },
            "rawvalue": "1754357218",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:26 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_0-ebs",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_0-ebs",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 2295187584,
            "rawvalue": "2295187584",
            "source": "NONE",
            "source_info": null,
            "value": "2.14G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 1412307328,
            "rawvalue": "1412307328",
            "source": "NONE",
            "source_info": null,
            "value": "1.32G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 882880256,
            "rawvalue": "882880256",
            "source": "NONE",
            "source_info": null,
            "value": "842M"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 1099511627776,
            "rawvalue": "1099511627776",
            "source": "LOCAL",
            "source_info": null,
            "value": "1T"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "2.14",
            "rawvalue": "2.14",
            "source": "NONE",
            "source_info": null,
            "value": "2.14x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357261000
            },
            "rawvalue": "1754357261",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:27 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_1-rook",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_1-rook",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 17898738944,
            "rawvalue": "17898738944",
            "source": "NONE",
            "source_info": null,
            "value": "16.7G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 10828343680,
            "rawvalue": "10828343680",
            "source": "NONE",
            "source_info": null,
            "value": "10.1G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 7070395264,
            "rawvalue": "7070395264",
            "source": "NONE",
            "source_info": null,
            "value": "6.58G"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 858993459200,
            "rawvalue": "858993459200",
            "source": "LOCAL",
            "source_info": null,
            "value": "800G"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "1.57",
            "rawvalue": "1.57",
            "source": "NONE",
            "source_info": null,
            "value": "1.57x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357314000
            },
            "rawvalue": "1754357314",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:28 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_2-ebs",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_2-ebs",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 3353085376,
            "rawvalue": "3353085376",
            "source": "NONE",
            "source_info": null,
            "value": "3.12G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 2231311552,
            "rawvalue": "2231311552",
            "source": "NONE",
            "source_info": null,
            "value": "2.08G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 1121773824,
            "rawvalue": "1121773824",
            "source": "NONE",
            "source_info": null,
            "value": "1.04G"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 1099511627776,
            "rawvalue": "1099511627776",
            "source": "LOCAL",
            "source_info": null,
            "value": "1T"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "1.76",
            "rawvalue": "1.76",
            "source": "NONE",
            "source_info": null,
            "value": "1.76x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357313000
            },
            "rawvalue": "1754357313",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:28 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_2-boot",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_2-boot",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 25578723904,
            "rawvalue": "25578723904",
            "source": "NONE",
            "source_info": null,
            "value": "23.8G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 19308178560,
            "rawvalue": "19308178560",
            "source": "NONE",
            "source_info": null,
            "value": "18.0G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 6270545344,
            "rawvalue": "6270545344",
            "source": "NONE",
            "source_info": null,
            "value": "5.84G"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 268435456000,
            "rawvalue": "268435456000",
            "source": "LOCAL",
            "source_info": null,
            "value": "250G"
          }
        },
        {
          "available": {
            "parsed": 16757821094208,
            "rawvalue": "16757821094208",
            "source": "NONE",
            "source_info": null,
            "value": "15.2T"
          },
          "checksum": {
            "parsed": true,
            "rawvalue": "on",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "ON"
          },
          "children": [],
          "comments": {
            "parsed": "",
            "rawvalue": "",
            "source": "INHERITED",
            "value": ""
          },
          "compression": {
            "parsed": "lz4",
            "rawvalue": "lz4",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "LZ4"
          },
          "compressratio": {
            "parsed": "2.15",
            "rawvalue": "2.15",
            "source": "NONE",
            "source_info": null,
            "value": "2.15x"
          },
          "copies": {
            "parsed": 1,
            "rawvalue": "1",
            "source": "INHERITED",
            "source_info": "flashstor/VM",
            "value": "1"
          },
          "creation": {
            "parsed": {
              "$date": 1754357216000
            },
            "rawvalue": "1754357216",
            "source": "NONE",
            "source_info": null,
            "value": "Mon Aug  4 21:26 2025"
          },
          "deduplication": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "encrypted": false,
          "encryption_algorithm": {
            "parsed": "off",
            "rawvalue": "off",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "encryption_root": null,
          "id": "flashstor/VM/k8s_0-rook",
          "key_format": {
            "parsed": "none",
            "rawvalue": "none",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "key_loaded": false,
          "locked": false,
          "mountpoint": null,
          "name": "flashstor/VM/k8s_0-rook",
          "origin": {
            "parsed": "",
            "rawvalue": "",
            "source": "NONE",
            "source_info": null,
            "value": ""
          },
          "pbkdf2iters": {
            "parsed": "0",
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": "0"
          },
          "pool": "flashstor",
          "readonly": {
            "parsed": false,
            "rawvalue": "off",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "OFF"
          },
          "refreservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "reservation": {
            "parsed": null,
            "rawvalue": "0",
            "source": "DEFAULT",
            "source_info": null,
            "value": null
          },
          "snapdev": {
            "parsed": "hidden",
            "rawvalue": "hidden",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "HIDDEN"
          },
          "sync": {
            "parsed": "standard",
            "rawvalue": "standard",
            "source": "INHERITED",
            "source_info": "flashstor",
            "value": "STANDARD"
          },
          "type": "VOLUME",
          "used": {
            "parsed": 17874595904,
            "rawvalue": "17874595904",
            "source": "NONE",
            "source_info": null,
            "value": "16.6G"
          },
          "usedbychildren": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbydataset": {
            "parsed": 10765346944,
            "rawvalue": "10765346944",
            "source": "NONE",
            "source_info": null,
            "value": "10.0G"
          },
          "usedbyrefreservation": {
            "parsed": 0,
            "rawvalue": "0",
            "source": "NONE",
            "source_info": null,
            "value": "0B"
          },
          "usedbysnapshots": {
            "parsed": 7109248960,
            "rawvalue": "7109248960",
            "source": "NONE",
            "source_info": null,
            "value": "6.62G"
          },
          "user_properties": {},
          "volblocksize": {
            "parsed": 16384,
            "rawvalue": "16384",
            "source": "DEFAULT",
            "source_info": null,
            "value": "16K"
          },
          "volsize": {
            "parsed": 858993459200,
            "rawvalue": "858993459200",
            "source": "LOCAL",
            "source_info": null,
            "value": "800G"
          }
        }
      ],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "LOCAL",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.88",
        "rawvalue": "1.88",
        "source": "NONE",
        "source_info": null,
        "value": "1.88x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "LOCAL",
        "source_info": null,
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754057115000
        },
        "rawvalue": "1754057115",
        "source": "NONE",
        "source_info": null,
        "value": "Fri Aug  1 10:05 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "exec": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "id": "flashstor/VM",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": "/mnt/flashstor/VM",
      "name": "flashstor/VM",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "quota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "recordsize": {
        "parsed": 131072,
        "rawvalue": "131072",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "128K"
      },
      "refquota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "snapdir": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "special_small_block_size": {
        "parsed": "32768",
        "rawvalue": "32768",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "32K"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "FILESYSTEM",
      "used": {
        "parsed": 141223136256,
        "rawvalue": "141223136256",
        "source": "NONE",
        "source_info": null,
        "value": "132G"
      },
      "usedbychildren": {
        "parsed": 141222972416,
        "rawvalue": "141222972416",
        "source": "NONE",
        "source_info": null,
        "value": "132G"
      },
      "usedbydataset": {
        "parsed": 98304,
        "rawvalue": "98304",
        "source": "NONE",
        "source_info": null,
        "value": "96K"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 65536,
        "rawvalue": "65536",
        "source": "NONE",
        "source_info": null,
        "value": "64K"
      },
      "user_properties": {},
      "xattr": {
        "parsed": true,
        "rawvalue": "on",
        "source": "LOCAL",
        "source_info": null,
        "value": "ON"
      }
    },
    {
      "aclmode": {
        "parsed": "discard",
        "rawvalue": "discard",
        "source": "DEFAULT",
        "source_info": null,
        "value": "DISCARD"
      },
      "acltype": {
        "parsed": "posix",
        "rawvalue": "posix",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "POSIX"
      },
      "atime": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "casesensitivity": {
        "parsed": "sensitive",
        "rawvalue": "sensitive",
        "source": "NONE",
        "source_info": null,
        "value": "SENSITIVE"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.00",
        "rawvalue": "1.00",
        "source": "NONE",
        "source_info": null,
        "value": "1.00x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754057700000
        },
        "rawvalue": "1754057700",
        "source": "NONE",
        "source_info": null,
        "value": "Fri Aug  1 10:15 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "exec": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "id": "flashstor/ISO",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": "/mnt/flashstor/ISO",
      "name": "flashstor/ISO",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "quota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "recordsize": {
        "parsed": 131072,
        "rawvalue": "131072",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "128K"
      },
      "refquota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "snapdir": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "special_small_block_size": {
        "parsed": "32768",
        "rawvalue": "32768",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "32K"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "FILESYSTEM",
      "used": {
        "parsed": 303955392,
        "rawvalue": "303955392",
        "source": "NONE",
        "source_info": null,
        "value": "290M"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 303898048,
        "rawvalue": "303898048",
        "source": "NONE",
        "source_info": null,
        "value": "290M"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 57344,
        "rawvalue": "57344",
        "source": "NONE",
        "source_info": null,
        "value": "56K"
      },
      "user_properties": {},
      "xattr": {
        "parsed": true,
        "rawvalue": "on",
        "source": "LOCAL",
        "source_info": null,
        "value": "ON"
      }
    },
    {
      "aclmode": {
        "parsed": "discard",
        "rawvalue": "discard",
        "source": "DEFAULT",
        "source_info": null,
        "value": "DISCARD"
      },
      "acltype": {
        "parsed": "posix",
        "rawvalue": "posix",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "POSIX"
      },
      "atime": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "casesensitivity": {
        "parsed": "sensitive",
        "rawvalue": "sensitive",
        "source": "NONE",
        "source_info": null,
        "value": "SENSITIVE"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "LOCAL",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.00",
        "rawvalue": "1.00",
        "source": "NONE",
        "source_info": null,
        "value": "1.00x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "LOCAL",
        "source_info": null,
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754107825000
        },
        "rawvalue": "1754107825",
        "source": "NONE",
        "source_info": null,
        "value": "Sat Aug  2  0:10 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "exec": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "id": "flashstor/kubernetes",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": "/mnt/flashstor/kubernetes",
      "name": "flashstor/kubernetes",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "quota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "recordsize": {
        "parsed": 131072,
        "rawvalue": "131072",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "128K"
      },
      "refquota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "snapdir": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "special_small_block_size": {
        "parsed": "32768",
        "rawvalue": "32768",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "32K"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "FILESYSTEM",
      "used": {
        "parsed": 98304,
        "rawvalue": "98304",
        "source": "NONE",
        "source_info": null,
        "value": "96K"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 98304,
        "rawvalue": "98304",
        "source": "NONE",
        "source_info": null,
        "value": "96K"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "user_properties": {},
      "xattr": {
        "parsed": true,
        "rawvalue": "on",
        "source": "LOCAL",
        "source_info": null,
        "value": "ON"
      }
    },
    {
      "aclmode": {
        "parsed": "discard",
        "rawvalue": "discard",
        "source": "DEFAULT",
        "source_info": null,
        "value": "DISCARD"
      },
      "acltype": {
        "parsed": "posix",
        "rawvalue": "posix",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "POSIX"
      },
      "atime": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "casesensitivity": {
        "parsed": "sensitive",
        "rawvalue": "sensitive",
        "source": "NONE",
        "source_info": null,
        "value": "SENSITIVE"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "LOCAL",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.03",
        "rawvalue": "1.03",
        "source": "NONE",
        "source_info": null,
        "value": "1.03x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "LOCAL",
        "source_info": null,
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1752763799000
        },
        "rawvalue": "1752763799",
        "source": "NONE",
        "source_info": null,
        "value": "Thu Jul 17 10:49 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "exec": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "id": "flashstor/Volsync",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": "/mnt/flashstor/Volsync",
      "name": "flashstor/Volsync",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "quota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "recordsize": {
        "parsed": 131072,
        "rawvalue": "131072",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "128K"
      },
      "refquota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "snapdir": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "special_small_block_size": {
        "parsed": "32768",
        "rawvalue": "32768",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "32K"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "FILESYSTEM",
      "used": {
        "parsed": 4026410560,
        "rawvalue": "4026410560",
        "source": "NONE",
        "source_info": null,
        "value": "3.75G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 3919077696,
        "rawvalue": "3919077696",
        "source": "NONE",
        "source_info": null,
        "value": "3.65G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 107332864,
        "rawvalue": "107332864",
        "source": "NONE",
        "source_info": null,
        "value": "102M"
      },
      "user_properties": {},
      "xattr": {
        "parsed": true,
        "rawvalue": "on",
        "source": "LOCAL",
        "source_info": null,
        "value": "ON"
      }
    },
    {
      "aclmode": {
        "parsed": "discard",
        "rawvalue": "discard",
        "source": "DEFAULT",
        "source_info": null,
        "value": "DISCARD"
      },
      "acltype": {
        "parsed": "posix",
        "rawvalue": "posix",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "POSIX"
      },
      "atime": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "available": {
        "parsed": 1073602560,
        "rawvalue": "1073602560",
        "source": "NONE",
        "source_info": null,
        "value": "1024M"
      },
      "casesensitivity": {
        "parsed": "sensitive",
        "rawvalue": "sensitive",
        "source": "NONE",
        "source_info": null,
        "value": "SENSITIVE"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "LOCAL",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.00",
        "rawvalue": "1.00",
        "source": "NONE",
        "source_info": null,
        "value": "1.00x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "LOCAL",
        "source_info": null,
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1753816466000
        },
        "rawvalue": "1753816466",
        "source": "NONE",
        "source_info": null,
        "value": "Tue Jul 29 15:14 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "exec": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "id": "flashstor/onepassword-connect",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": "/mnt/flashstor/onepassword-connect",
      "name": "flashstor/onepassword-connect",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "quota": {
        "parsed": 1073741824,
        "rawvalue": "1073741824",
        "source": "LOCAL",
        "source_info": null,
        "value": "1G"
      },
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "recordsize": {
        "parsed": 131072,
        "rawvalue": "131072",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "128K"
      },
      "refquota": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "snapdir": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "special_small_block_size": {
        "parsed": "32768",
        "rawvalue": "32768",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "32K"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "FILESYSTEM",
      "used": {
        "parsed": 139264,
        "rawvalue": "139264",
        "source": "NONE",
        "source_info": null,
        "value": "136K"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 139264,
        "rawvalue": "139264",
        "source": "NONE",
        "source_info": null,
        "value": "136K"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "user_properties": {},
      "xattr": {
        "parsed": true,
        "rawvalue": "on",
        "source": "LOCAL",
        "source_info": null,
        "value": "ON"
      }
    }
  ],
  "comments": {
    "parsed": "",
    "rawvalue": "",
    "source": "LOCAL",
    "value": ""
  },
  "compression": {
    "parsed": "lz4",
    "rawvalue": "lz4",
    "source": "LOCAL",
    "source_info": null,
    "value": "LZ4"
  },
  "compressratio": {
    "parsed": "1.02",
    "rawvalue": "1.02",
    "source": "NONE",
    "source_info": null,
    "value": "1.02x"
  },
  "copies": {
    "parsed": 1,
    "rawvalue": "1",
    "source": "LOCAL",
    "source_info": null,
    "value": "1"
  },
  "creation": {
    "parsed": {
      "$date": 1720645363000
    },
    "rawvalue": "1720645363",
    "source": "NONE",
    "source_info": null,
    "value": "Wed Jul 10 17:02 2024"
  },
  "deduplication": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "LOCAL",
    "source_info": null,
    "value": "OFF"
  },
  "encrypted": false,
  "encryption_algorithm": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "encryption_root": null,
  "exec": {
    "parsed": true,
    "rawvalue": "on",
    "source": "LOCAL",
    "source_info": null,
    "value": "ON"
  },
  "id": "flashstor",
  "key_format": {
    "parsed": "none",
    "rawvalue": "none",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "key_loaded": false,
  "locked": false,
  "mountpoint": "/mnt/flashstor",
  "name": "flashstor",
  "origin": {
    "parsed": "",
    "rawvalue": "",
    "source": "NONE",
    "source_info": null,
    "value": ""
  },
  "pbkdf2iters": {
    "parsed": "0",
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": "0"
  },
  "pool": "flashstor",
  "quota": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "readonly": {
    "parsed": false,
    "rawvalue": "off",
    "source": "LOCAL",
    "source_info": null,
    "value": "OFF"
  },
  "recordsize": {
    "parsed": 131072,
    "rawvalue": "131072",
    "source": "LOCAL",
    "source_info": null,
    "value": "128K"
  },
  "refquota": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "refreservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "reservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "snapdev": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "LOCAL",
    "source_info": null,
    "value": "HIDDEN"
  },
  "snapdir": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "LOCAL",
    "source_info": null,
    "value": "HIDDEN"
  },
  "special_small_block_size": {
    "parsed": "32768",
    "rawvalue": "32768",
    "source": "LOCAL",
    "source_info": null,
    "value": "32K"
  },
  "sync": {
    "parsed": "standard",
    "rawvalue": "standard",
    "source": "LOCAL",
    "source_info": null,
    "value": "STANDARD"
  },
  "type": "FILESYSTEM",
  "used": {
    "parsed": 8006911862528,
    "rawvalue": "8006911862528",
    "source": "NONE",
    "source_info": null,
    "value": "7.28T"
  },
  "usedbychildren": {
    "parsed": 8006911465728,
    "rawvalue": "8006911465728",
    "source": "NONE",
    "source_info": null,
    "value": "7.28T"
  },
  "usedbydataset": {
    "parsed": 203264,
    "rawvalue": "203264",
    "source": "NONE",
    "source_info": null,
    "value": "198K"
  },
  "usedbyrefreservation": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbysnapshots": {
    "parsed": 193536,
    "rawvalue": "193536",
    "source": "NONE",
    "source_info": null,
    "value": "189K"
  },
  "user_properties": {},
  "xattr": {
    "parsed": true,
    "rawvalue": "on",
    "source": "LOCAL",
    "source_info": null,
    "value": "ON"
  }
}
```

### Dataset Example 2

```json
{
  "aclmode": {
    "parsed": "discard",
    "rawvalue": "discard",
    "source": "DEFAULT",
    "source_info": null,
    "value": "DISCARD"
  },
  "acltype": {
    "parsed": "posix",
    "rawvalue": "posix",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "POSIX"
  },
  "atime": {
    "parsed": false,
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "available": {
    "parsed": 1024866432,
    "rawvalue": "1024866432",
    "source": "NONE",
    "source_info": null,
    "value": "977M"
  },
  "casesensitivity": {
    "parsed": "sensitive",
    "rawvalue": "sensitive",
    "source": "NONE",
    "source_info": null,
    "value": "SENSITIVE"
  },
  "checksum": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  },
  "children": [],
  "comments": {
    "parsed": "",
    "rawvalue": "",
    "source": "LOCAL",
    "value": ""
  },
  "compression": {
    "parsed": "lz4",
    "rawvalue": "lz4",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "LZ4"
  },
  "compressratio": {
    "parsed": "2.39",
    "rawvalue": "2.39",
    "source": "NONE",
    "source_info": null,
    "value": "2.39x"
  },
  "copies": {
    "parsed": 1,
    "rawvalue": "1",
    "source": "LOCAL",
    "source_info": null,
    "value": "1"
  },
  "creation": {
    "parsed": {
      "$date": 1753060422000
    },
    "rawvalue": "1753060422",
    "source": "NONE",
    "source_info": null,
    "value": "Sun Jul 20 21:13 2025"
  },
  "deduplication": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "encrypted": false,
  "encryption_algorithm": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "encryption_root": null,
  "exec": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  },
  "id": "flashstor/scripts",
  "key_format": {
    "parsed": "none",
    "rawvalue": "none",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "key_loaded": false,
  "locked": false,
  "mountpoint": "/mnt/flashstor/scripts",
  "name": "flashstor/scripts",
  "origin": {
    "parsed": "",
    "rawvalue": "",
    "source": "NONE",
    "source_info": null,
    "value": ""
  },
  "pbkdf2iters": {
    "parsed": "0",
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": "0"
  },
  "pool": "flashstor",
  "quota": {
    "parsed": 1073741824,
    "rawvalue": "1073741824",
    "source": "LOCAL",
    "source_info": null,
    "value": "1G"
  },
  "readonly": {
    "parsed": false,
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "recordsize": {
    "parsed": 131072,
    "rawvalue": "131072",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "128K"
  },
  "refquota": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "refreservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "reservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "snapdev": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "HIDDEN"
  },
  "snapdir": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "HIDDEN"
  },
  "special_small_block_size": {
    "parsed": "32768",
    "rawvalue": "32768",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "32K"
  },
  "sync": {
    "parsed": "standard",
    "rawvalue": "standard",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "STANDARD"
  },
  "type": "FILESYSTEM",
  "used": {
    "parsed": 48875392,
    "rawvalue": "48875392",
    "source": "NONE",
    "source_info": null,
    "value": "46.6M"
  },
  "usedbychildren": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbydataset": {
    "parsed": 48875392,
    "rawvalue": "48875392",
    "source": "NONE",
    "source_info": null,
    "value": "46.6M"
  },
  "usedbyrefreservation": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbysnapshots": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "user_properties": {},
  "xattr": {
    "parsed": true,
    "rawvalue": "on",
    "source": "LOCAL",
    "source_info": null,
    "value": "ON"
  }
}
```

### Dataset Example 3

```json
{
  "aclmode": {
    "parsed": "discard",
    "rawvalue": "discard",
    "source": "DEFAULT",
    "source_info": null,
    "value": "DISCARD"
  },
  "acltype": {
    "parsed": "posix",
    "rawvalue": "posix",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "POSIX"
  },
  "atime": {
    "parsed": false,
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "available": {
    "parsed": 16757821094208,
    "rawvalue": "16757821094208",
    "source": "NONE",
    "source_info": null,
    "value": "15.2T"
  },
  "casesensitivity": {
    "parsed": "sensitive",
    "rawvalue": "sensitive",
    "source": "NONE",
    "source_info": null,
    "value": "SENSITIVE"
  },
  "checksum": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  },
  "children": [],
  "comments": {
    "parsed": "",
    "rawvalue": "",
    "source": "INHERITED",
    "value": ""
  },
  "compression": {
    "parsed": "lz4",
    "rawvalue": "lz4",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "LZ4"
  },
  "compressratio": {
    "parsed": "1.00",
    "rawvalue": "1.00",
    "source": "NONE",
    "source_info": null,
    "value": "1.00x"
  },
  "copies": {
    "parsed": 1,
    "rawvalue": "1",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "1"
  },
  "creation": {
    "parsed": {
      "$date": 1720717601000
    },
    "rawvalue": "1720717601",
    "source": "NONE",
    "source_info": null,
    "value": "Thu Jul 11 13:06 2024"
  },
  "deduplication": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "encrypted": false,
  "encryption_algorithm": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "encryption_root": null,
  "exec": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  },
  "id": "flashstor/data",
  "key_format": {
    "parsed": "none",
    "rawvalue": "none",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "key_loaded": false,
  "locked": false,
  "mountpoint": "/mnt/flashstor/data",
  "name": "flashstor/data",
  "origin": {
    "parsed": "",
    "rawvalue": "",
    "source": "NONE",
    "source_info": null,
    "value": ""
  },
  "pbkdf2iters": {
    "parsed": "0",
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": "0"
  },
  "pool": "flashstor",
  "quota": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "readonly": {
    "parsed": false,
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "recordsize": {
    "parsed": 131072,
    "rawvalue": "131072",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "128K"
  },
  "refquota": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "refreservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "reservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "snapdev": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "HIDDEN"
  },
  "snapdir": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "HIDDEN"
  },
  "special_small_block_size": {
    "parsed": "32768",
    "rawvalue": "32768",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "32K"
  },
  "sync": {
    "parsed": "standard",
    "rawvalue": "standard",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "STANDARD"
  },
  "type": "FILESYSTEM",
  "used": {
    "parsed": 7764289774016,
    "rawvalue": "7764289774016",
    "source": "NONE",
    "source_info": null,
    "value": "7.06T"
  },
  "usedbychildren": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbydataset": {
    "parsed": 7761836177408,
    "rawvalue": "7761836177408",
    "source": "NONE",
    "source_info": null,
    "value": "7.06T"
  },
  "usedbyrefreservation": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbysnapshots": {
    "parsed": 2453596608,
    "rawvalue": "2453596608",
    "source": "NONE",
    "source_info": null,
    "value": "2.29G"
  },
  "user_properties": {},
  "xattr": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  }
}
```

### Dataset Example 4

```json
{
  "aclmode": {
    "parsed": "discard",
    "rawvalue": "discard",
    "source": "DEFAULT",
    "source_info": null,
    "value": "DISCARD"
  },
  "acltype": {
    "parsed": "posix",
    "rawvalue": "posix",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "POSIX"
  },
  "atime": {
    "parsed": false,
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "available": {
    "parsed": 16757821094208,
    "rawvalue": "16757821094208",
    "source": "NONE",
    "source_info": null,
    "value": "15.2T"
  },
  "casesensitivity": {
    "parsed": "sensitive",
    "rawvalue": "sensitive",
    "source": "NONE",
    "source_info": null,
    "value": "SENSITIVE"
  },
  "checksum": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  },
  "children": [
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.72",
        "rawvalue": "1.72",
        "source": "NONE",
        "source_info": null,
        "value": "1.72x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357217000
        },
        "rawvalue": "1754357217",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:26 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_0-boot",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_0-boot",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 26502875456,
        "rawvalue": "26502875456",
        "source": "NONE",
        "source_info": null,
        "value": "24.7G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 20398826560,
        "rawvalue": "20398826560",
        "source": "NONE",
        "source_info": null,
        "value": "19.0G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 6104048896,
        "rawvalue": "6104048896",
        "source": "NONE",
        "source_info": null,
        "value": "5.68G"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 268435456000,
        "rawvalue": "268435456000",
        "source": "LOCAL",
        "source_info": null,
        "value": "250G"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.53",
        "rawvalue": "1.53",
        "source": "NONE",
        "source_info": null,
        "value": "1.53x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357260000
        },
        "rawvalue": "1754357260",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:27 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_1-ebs",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_1-ebs",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 1103523584,
        "rawvalue": "1103523584",
        "source": "NONE",
        "source_info": null,
        "value": "1.03G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 1099011712,
        "rawvalue": "1099011712",
        "source": "NONE",
        "source_info": null,
        "value": "1.02G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 4511872,
        "rawvalue": "4511872",
        "source": "NONE",
        "source_info": null,
        "value": "4.30M"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 1099511627776,
        "rawvalue": "1099511627776",
        "source": "LOCAL",
        "source_info": null,
        "value": "1T"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "2.15",
        "rawvalue": "2.15",
        "source": "NONE",
        "source_info": null,
        "value": "2.15x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357315000
        },
        "rawvalue": "1754357315",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:28 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_2-rook",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_2-rook",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 17887698432,
        "rawvalue": "17887698432",
        "source": "NONE",
        "source_info": null,
        "value": "16.7G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 10771042752,
        "rawvalue": "10771042752",
        "source": "NONE",
        "source_info": null,
        "value": "10.0G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 7116655680,
        "rawvalue": "7116655680",
        "source": "NONE",
        "source_info": null,
        "value": "6.63G"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 858993459200,
        "rawvalue": "858993459200",
        "source": "LOCAL",
        "source_info": null,
        "value": "800G"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.72",
        "rawvalue": "1.72",
        "source": "NONE",
        "source_info": null,
        "value": "1.72x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357262000
        },
        "rawvalue": "1754357262",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:27 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_1-boot",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_1-boot",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 28728543232,
        "rawvalue": "28728543232",
        "source": "NONE",
        "source_info": null,
        "value": "26.8G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 22668408832,
        "rawvalue": "22668408832",
        "source": "NONE",
        "source_info": null,
        "value": "21.1G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 6060134400,
        "rawvalue": "6060134400",
        "source": "NONE",
        "source_info": null,
        "value": "5.64G"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 268435456000,
        "rawvalue": "268435456000",
        "source": "LOCAL",
        "source_info": null,
        "value": "250G"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.60",
        "rawvalue": "1.60",
        "source": "NONE",
        "source_info": null,
        "value": "1.60x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357218000
        },
        "rawvalue": "1754357218",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:26 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_0-ebs",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_0-ebs",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 2295187584,
        "rawvalue": "2295187584",
        "source": "NONE",
        "source_info": null,
        "value": "2.14G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 1412307328,
        "rawvalue": "1412307328",
        "source": "NONE",
        "source_info": null,
        "value": "1.32G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 882880256,
        "rawvalue": "882880256",
        "source": "NONE",
        "source_info": null,
        "value": "842M"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 1099511627776,
        "rawvalue": "1099511627776",
        "source": "LOCAL",
        "source_info": null,
        "value": "1T"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "2.14",
        "rawvalue": "2.14",
        "source": "NONE",
        "source_info": null,
        "value": "2.14x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357261000
        },
        "rawvalue": "1754357261",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:27 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_1-rook",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_1-rook",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 17898738944,
        "rawvalue": "17898738944",
        "source": "NONE",
        "source_info": null,
        "value": "16.7G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 10828343680,
        "rawvalue": "10828343680",
        "source": "NONE",
        "source_info": null,
        "value": "10.1G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 7070395264,
        "rawvalue": "7070395264",
        "source": "NONE",
        "source_info": null,
        "value": "6.58G"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 858993459200,
        "rawvalue": "858993459200",
        "source": "LOCAL",
        "source_info": null,
        "value": "800G"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.57",
        "rawvalue": "1.57",
        "source": "NONE",
        "source_info": null,
        "value": "1.57x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357314000
        },
        "rawvalue": "1754357314",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:28 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_2-ebs",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_2-ebs",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 3353085376,
        "rawvalue": "3353085376",
        "source": "NONE",
        "source_info": null,
        "value": "3.12G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 2231311552,
        "rawvalue": "2231311552",
        "source": "NONE",
        "source_info": null,
        "value": "2.08G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 1121773824,
        "rawvalue": "1121773824",
        "source": "NONE",
        "source_info": null,
        "value": "1.04G"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 1099511627776,
        "rawvalue": "1099511627776",
        "source": "LOCAL",
        "source_info": null,
        "value": "1T"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "1.76",
        "rawvalue": "1.76",
        "source": "NONE",
        "source_info": null,
        "value": "1.76x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357313000
        },
        "rawvalue": "1754357313",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:28 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_2-boot",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_2-boot",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 25578723904,
        "rawvalue": "25578723904",
        "source": "NONE",
        "source_info": null,
        "value": "23.8G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 19308178560,
        "rawvalue": "19308178560",
        "source": "NONE",
        "source_info": null,
        "value": "18.0G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 6270545344,
        "rawvalue": "6270545344",
        "source": "NONE",
        "source_info": null,
        "value": "5.84G"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 268435456000,
        "rawvalue": "268435456000",
        "source": "LOCAL",
        "source_info": null,
        "value": "250G"
      }
    },
    {
      "available": {
        "parsed": 16757821094208,
        "rawvalue": "16757821094208",
        "source": "NONE",
        "source_info": null,
        "value": "15.2T"
      },
      "checksum": {
        "parsed": true,
        "rawvalue": "on",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "ON"
      },
      "children": [],
      "comments": {
        "parsed": "",
        "rawvalue": "",
        "source": "INHERITED",
        "value": ""
      },
      "compression": {
        "parsed": "lz4",
        "rawvalue": "lz4",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "LZ4"
      },
      "compressratio": {
        "parsed": "2.15",
        "rawvalue": "2.15",
        "source": "NONE",
        "source_info": null,
        "value": "2.15x"
      },
      "copies": {
        "parsed": 1,
        "rawvalue": "1",
        "source": "INHERITED",
        "source_info": "flashstor/VM",
        "value": "1"
      },
      "creation": {
        "parsed": {
          "$date": 1754357216000
        },
        "rawvalue": "1754357216",
        "source": "NONE",
        "source_info": null,
        "value": "Mon Aug  4 21:26 2025"
      },
      "deduplication": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "encrypted": false,
      "encryption_algorithm": {
        "parsed": "off",
        "rawvalue": "off",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "encryption_root": null,
      "id": "flashstor/VM/k8s_0-rook",
      "key_format": {
        "parsed": "none",
        "rawvalue": "none",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "key_loaded": false,
      "locked": false,
      "mountpoint": null,
      "name": "flashstor/VM/k8s_0-rook",
      "origin": {
        "parsed": "",
        "rawvalue": "",
        "source": "NONE",
        "source_info": null,
        "value": ""
      },
      "pbkdf2iters": {
        "parsed": "0",
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": "0"
      },
      "pool": "flashstor",
      "readonly": {
        "parsed": false,
        "rawvalue": "off",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "OFF"
      },
      "refreservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "reservation": {
        "parsed": null,
        "rawvalue": "0",
        "source": "DEFAULT",
        "source_info": null,
        "value": null
      },
      "snapdev": {
        "parsed": "hidden",
        "rawvalue": "hidden",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "HIDDEN"
      },
      "sync": {
        "parsed": "standard",
        "rawvalue": "standard",
        "source": "INHERITED",
        "source_info": "flashstor",
        "value": "STANDARD"
      },
      "type": "VOLUME",
      "used": {
        "parsed": 17874595904,
        "rawvalue": "17874595904",
        "source": "NONE",
        "source_info": null,
        "value": "16.6G"
      },
      "usedbychildren": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbydataset": {
        "parsed": 10765346944,
        "rawvalue": "10765346944",
        "source": "NONE",
        "source_info": null,
        "value": "10.0G"
      },
      "usedbyrefreservation": {
        "parsed": 0,
        "rawvalue": "0",
        "source": "NONE",
        "source_info": null,
        "value": "0B"
      },
      "usedbysnapshots": {
        "parsed": 7109248960,
        "rawvalue": "7109248960",
        "source": "NONE",
        "source_info": null,
        "value": "6.62G"
      },
      "user_properties": {},
      "volblocksize": {
        "parsed": 16384,
        "rawvalue": "16384",
        "source": "DEFAULT",
        "source_info": null,
        "value": "16K"
      },
      "volsize": {
        "parsed": 858993459200,
        "rawvalue": "858993459200",
        "source": "LOCAL",
        "source_info": null,
        "value": "800G"
      }
    }
  ],
  "comments": {
    "parsed": "",
    "rawvalue": "",
    "source": "LOCAL",
    "value": ""
  },
  "compression": {
    "parsed": "lz4",
    "rawvalue": "lz4",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "LZ4"
  },
  "compressratio": {
    "parsed": "1.88",
    "rawvalue": "1.88",
    "source": "NONE",
    "source_info": null,
    "value": "1.88x"
  },
  "copies": {
    "parsed": 1,
    "rawvalue": "1",
    "source": "LOCAL",
    "source_info": null,
    "value": "1"
  },
  "creation": {
    "parsed": {
      "$date": 1754057115000
    },
    "rawvalue": "1754057115",
    "source": "NONE",
    "source_info": null,
    "value": "Fri Aug  1 10:05 2025"
  },
  "deduplication": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "encrypted": false,
  "encryption_algorithm": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "encryption_root": null,
  "exec": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  },
  "id": "flashstor/VM",
  "key_format": {
    "parsed": "none",
    "rawvalue": "none",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "key_loaded": false,
  "locked": false,
  "mountpoint": "/mnt/flashstor/VM",
  "name": "flashstor/VM",
  "origin": {
    "parsed": "",
    "rawvalue": "",
    "source": "NONE",
    "source_info": null,
    "value": ""
  },
  "pbkdf2iters": {
    "parsed": "0",
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": "0"
  },
  "pool": "flashstor",
  "quota": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "readonly": {
    "parsed": false,
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "recordsize": {
    "parsed": 131072,
    "rawvalue": "131072",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "128K"
  },
  "refquota": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "refreservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "reservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "snapdev": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "HIDDEN"
  },
  "snapdir": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "HIDDEN"
  },
  "special_small_block_size": {
    "parsed": "32768",
    "rawvalue": "32768",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "32K"
  },
  "sync": {
    "parsed": "standard",
    "rawvalue": "standard",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "STANDARD"
  },
  "type": "FILESYSTEM",
  "used": {
    "parsed": 141223136256,
    "rawvalue": "141223136256",
    "source": "NONE",
    "source_info": null,
    "value": "132G"
  },
  "usedbychildren": {
    "parsed": 141222972416,
    "rawvalue": "141222972416",
    "source": "NONE",
    "source_info": null,
    "value": "132G"
  },
  "usedbydataset": {
    "parsed": 98304,
    "rawvalue": "98304",
    "source": "NONE",
    "source_info": null,
    "value": "96K"
  },
  "usedbyrefreservation": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbysnapshots": {
    "parsed": 65536,
    "rawvalue": "65536",
    "source": "NONE",
    "source_info": null,
    "value": "64K"
  },
  "user_properties": {},
  "xattr": {
    "parsed": true,
    "rawvalue": "on",
    "source": "LOCAL",
    "source_info": null,
    "value": "ON"
  }
}
```

### Dataset Example 5

```json
{
  "available": {
    "parsed": 16757821094208,
    "rawvalue": "16757821094208",
    "source": "NONE",
    "source_info": null,
    "value": "15.2T"
  },
  "checksum": {
    "parsed": true,
    "rawvalue": "on",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "ON"
  },
  "children": [],
  "comments": {
    "parsed": "",
    "rawvalue": "",
    "source": "INHERITED",
    "value": ""
  },
  "compression": {
    "parsed": "lz4",
    "rawvalue": "lz4",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "LZ4"
  },
  "compressratio": {
    "parsed": "1.72",
    "rawvalue": "1.72",
    "source": "NONE",
    "source_info": null,
    "value": "1.72x"
  },
  "copies": {
    "parsed": 1,
    "rawvalue": "1",
    "source": "INHERITED",
    "source_info": "flashstor/VM",
    "value": "1"
  },
  "creation": {
    "parsed": {
      "$date": 1754357217000
    },
    "rawvalue": "1754357217",
    "source": "NONE",
    "source_info": null,
    "value": "Mon Aug  4 21:26 2025"
  },
  "deduplication": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "encrypted": false,
  "encryption_algorithm": {
    "parsed": "off",
    "rawvalue": "off",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "encryption_root": null,
  "id": "flashstor/VM/k8s_0-boot",
  "key_format": {
    "parsed": "none",
    "rawvalue": "none",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "key_loaded": false,
  "locked": false,
  "mountpoint": null,
  "name": "flashstor/VM/k8s_0-boot",
  "origin": {
    "parsed": "",
    "rawvalue": "",
    "source": "NONE",
    "source_info": null,
    "value": ""
  },
  "pbkdf2iters": {
    "parsed": "0",
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": "0"
  },
  "pool": "flashstor",
  "readonly": {
    "parsed": false,
    "rawvalue": "off",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "OFF"
  },
  "refreservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "reservation": {
    "parsed": null,
    "rawvalue": "0",
    "source": "DEFAULT",
    "source_info": null,
    "value": null
  },
  "snapdev": {
    "parsed": "hidden",
    "rawvalue": "hidden",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "HIDDEN"
  },
  "sync": {
    "parsed": "standard",
    "rawvalue": "standard",
    "source": "INHERITED",
    "source_info": "flashstor",
    "value": "STANDARD"
  },
  "type": "VOLUME",
  "used": {
    "parsed": 26502875456,
    "rawvalue": "26502875456",
    "source": "NONE",
    "source_info": null,
    "value": "24.7G"
  },
  "usedbychildren": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbydataset": {
    "parsed": 20398826560,
    "rawvalue": "20398826560",
    "source": "NONE",
    "source_info": null,
    "value": "19.0G"
  },
  "usedbyrefreservation": {
    "parsed": 0,
    "rawvalue": "0",
    "source": "NONE",
    "source_info": null,
    "value": "0B"
  },
  "usedbysnapshots": {
    "parsed": 6104048896,
    "rawvalue": "6104048896",
    "source": "NONE",
    "source_info": null,
    "value": "5.68G"
  },
  "user_properties": {},
  "volblocksize": {
    "parsed": 16384,
    "rawvalue": "16384",
    "source": "DEFAULT",
    "source_info": null,
    "value": "16K"
  },
  "volsize": {
    "parsed": 268435456000,
    "rawvalue": "268435456000",
    "source": "LOCAL",
    "source_info": null,
    "value": "250G"
  }
}
```

## API Method Details

### vm.query

**Successful Response:**

```json
{
  "error": {
    "code": -32602,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": [
        [
          "filters",
          "Input should be a valid list",
          22
        ]
      ],
      "reason": "[EINVAL] filters: Input should be a valid list\n",
      "trace": {
        "class": "ValidationErrors",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py\", line 91, in wrapped\n    args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 25, in accept_params\n    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 84, in validate_model\n    raise verrors from None\nmiddlewared.service_exception.ValidationErrors: [EINVAL] filters: Input should be a valid list\n\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_validation_error(id_, sys.exc_info(), list(e))\n",
            "lineno": 335,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "ValidationErrors([ValidationError('filters', 'Input should be a valid list', 22)])",
              "id_": "3",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f8d0\u003e",
              "params": "[{}]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.query of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "mock": "None",
              "params": "[{}]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f8d0\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17f420\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17f6a0\u003e",
              "method": "'vm.query'",
              "methodobj": "\u003cbound method CRUDService.query of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "params": "[{}]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c4c12b6c0\u003e}",
              "methodobj": "\u003cbound method CRUDService.query of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "name": "'vm.query'",
              "params": "[{}]",
              "prepared_call": "PreparedCall(args=[{}], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          },
          {
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py",
            "line": "                args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n",
            "lineno": 91,
            "locals": {
              "accepts": "\u003cclass 'middlewared.api.v25_04_2.common.QueryArgs'\u003e",
              "args": "('***', '***')",
              "args_index": "1",
              "func": "\u003cfunction CRUDService.query at 0x7f3cf93df1a0\u003e"
            },
            "method": "wrapped",
            "varargspec": "args"
          },
          {
            "argspec": [
              "model",
              "args",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n",
            "lineno": 25,
            "locals": {
              "args": "({},)",
              "args_as_dict": "{'filters': {}}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "model": "\u003cclass 'middlewared.api.v25_04_2.common.QueryArgs'\u003e"
            },
            "method": "accept_params"
          },
          {
            "argspec": [
              "model",
              "data",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "        raise verrors from None\n",
            "lineno": 84,
            "locals": {
              "data": "{'filters': {}}",
              "error": "{'type': 'list_type', 'loc': ('filters',), 'msg': 'Input should be a valid list', 'input': {}, 'url': 'https://errors.pydantic.dev/2.9/v/list_type'}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "loc": "['filters']",
              "model": "\u003cclass 'middlewared.api.v25_04_2.common.QueryArgs'\u003e",
              "msg": "'Input should be a valid list'",
              "verrors": "ValidationErrors([ValidationError('filters', 'Input should be a valid list', 22)])"
            },
            "method": "validate_model"
          }
        ],
        "repr": "ValidationErrors([ValidationError('filters', 'Input should be a valid list', 22)])"
      }
    },
    "message": "Invalid params"
  },
  "id": 3,
  "jsonrpc": "2.0"
}
```

**Working Parameters:**

```json
{}
```

---

### vm.create

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.create() missing 1 required positional argument: 'data'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.create() missing 1 required positional argument: 'data'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")",
              "extra": "None",
              "id_": "4",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f010\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f010\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17e840\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17d080\u003e",
              "method": "'vm.create'",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c0e0b8d40\u003e}",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "name": "'vm.create'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3c0e0b8d40\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 4,
  "jsonrpc": "2.0"
}
```

---

### vm.update

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\")",
              "extra": "None",
              "id_": "5",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5fdd0\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.update of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5fdd0\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17f7e0\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17c0e0\u003e",
              "method": "'vm.update'",
              "methodobj": "\u003cbound method CRUDService.update of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c0e0b9880\u003e}",
              "methodobj": "\u003cbound method CRUDService.update of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "name": "'vm.update'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3c0e0b9880\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 5,
  "jsonrpc": "2.0"
}
```

---

### vm.delete

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.delete() missing 1 required positional argument: 'id_'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.delete() missing 1 required positional argument: 'id_'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")",
              "extra": "None",
              "id_": "6",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f190\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f190\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17f9c0\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17f2e0\u003e",
              "method": "'vm.delete'",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c0e0bb240\u003e}",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "name": "'vm.delete'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3c0e0bb240\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 6,
  "jsonrpc": "2.0"
}
```

---

### vm.start

**Successful Response:**

```json
{
  "error": {
    "code": -32602,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": [
        [
          "id",
          "Field required",
          22
        ]
      ],
      "reason": "[EINVAL] id: Field required\n",
      "trace": {
        "class": "ValidationErrors",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py\", line 91, in wrapped\n    args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 25, in accept_params\n    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 84, in validate_model\n    raise verrors from None\nmiddlewared.service_exception.ValidationErrors: [EINVAL] id: Field required\n\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_validation_error(id_, sys.exc_info(), list(e))\n",
            "lineno": 335,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "ValidationErrors([ValidationError('id', 'Field required', 22)])",
              "id_": "7",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5fc90\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method VMService.start of \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5fc90\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17d580\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17f920\u003e",
              "method": "'vm.start'",
              "methodobj": "\u003cbound method VMService.start of \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3cc88a5ec0\u003e}",
              "methodobj": "\u003cbound method VMService.start of \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e\u003e",
              "name": "'vm.start'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          },
          {
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py",
            "line": "                args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n",
            "lineno": 91,
            "locals": {
              "accepts": "\u003cclass 'middlewared.api.v25_04_2.vm.VMStartArgs'\u003e",
              "args": "('***',)",
              "args_index": "1",
              "func": "\u003cfunction VMService.start at 0x7f3ccd3fde40\u003e"
            },
            "method": "wrapped",
            "varargspec": "args"
          },
          {
            "argspec": [
              "model",
              "args",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n",
            "lineno": 25,
            "locals": {
              "args": "()",
              "args_as_dict": "{}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "model": "\u003cclass 'middlewared.api.v25_04_2.vm.VMStartArgs'\u003e"
            },
            "method": "accept_params"
          },
          {
            "argspec": [
              "model",
              "data",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "        raise verrors from None\n",
            "lineno": 84,
            "locals": {
              "data": "{}",
              "error": "{'type': 'missing', 'loc': ('id',), 'msg': 'Field required', 'input': {}, 'url': 'https://errors.pydantic.dev/2.9/v/missing'}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "loc": "['id']",
              "model": "\u003cclass 'middlewared.api.v25_04_2.vm.VMStartArgs'\u003e",
              "msg": "'Field required'",
              "verrors": "ValidationErrors([ValidationError('id', 'Field required', 22)])"
            },
            "method": "validate_model"
          }
        ],
        "repr": "ValidationErrors([ValidationError('id', 'Field required', 22)])"
      }
    },
    "message": "Invalid params"
  },
  "id": 7,
  "jsonrpc": "2.0"
}
```

---

### vm.stop

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "[EINVAL] Error handling job lock. This is most likely caused by invalid call arguments.",
      "trace": {
        "class": "CallError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/job.py\", line 373, in get_lock_name\n    lock_name = lock_name(self.args)\n                ^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/plugins/vm/vm_lifecycle.py\", line 67, in \u003clambda\u003e\n    @job(lock=lambda args: f'stop_vm_{args[0]}')\n                                      ~~~~^^^\nIndexError: list index out of range\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 713, in _call\n    prepared_call = self._call_prepare(name, serviceobj, methodobj, params, **kwargs)\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 690, in _call_prepare\n    job = self.jobs.add(job)\n          ^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/job.py\", line 123, in add\n    self.handle_lock(job)\n  File \"/usr/lib/python3/dist-packages/middlewared/job.py\", line 157, in handle_lock\n    name = job.get_lock_name()\n           ^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/job.py\", line 376, in get_lock_name\n    raise CallError(\"Error handling job lock. This is most likely caused by invalid call arguments.\",\nmiddlewared.service_exception.CallError: [EINVAL] Error handling job lock. This is most likely caused by invalid call arguments.\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", e.errno, str(e),\n",
            "lineno": 339,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "CallError('Error handling job lock. This is most likely caused by invalid call arguments.', 22)",
              "id_": "8",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc90ffd90\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method VMService.stop of \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc90ffd90\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17d120\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17d3a0\u003e",
              "method": "'vm.stop'",
              "methodobj": "\u003cbound method VMService.stop of \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "        prepared_call = self._call_prepare(name, serviceobj, methodobj, params, **kwargs)\n",
            "lineno": 713,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c0c4da840\u003e}",
              "methodobj": "\u003cbound method VMService.stop of \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e\u003e",
              "name": "'vm.stop'",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params",
              "app",
              "audit_callback",
              "job_on_progress_cb",
              "pipes",
              "in_event_loop"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "line": "                job = self.jobs.add(job)\n",
            "lineno": 690,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "args": "[]",
              "audit_callback": "\u003cbuilt-in method append of list object at 0x7f3c0c4da840\u003e",
              "in_event_loop": "True",
              "job": "\u003cmiddlewared.job.Job object at 0x7f3c0d0cb710\u003e",
              "job_on_progress_cb": "None",
              "job_options": "{'lock': \u003cfunction VMService.\u003clambda\u003e at 0x7f3ccd3fdf80\u003e, 'lock_queue_size': 5, 'logs': False, 'process': False, 'pipes': [], 'check_pipes': True, 'transient': False, 'description': None, 'abortable': False, 'read_roles': []}",
              "methodobj": "\u003cbound method VMService.stop of \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e\u003e",
              "name": "'vm.stop'",
              "params": "[]",
              "pipes": "None",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call_prepare"
          },
          {
            "argspec": [
              "self",
              "job"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/job.py",
            "line": "        self.handle_lock(job)\n",
            "lineno": 123,
            "locals": {
              "job": "\u003cmiddlewared.job.Job object at 0x7f3c0d0cb710\u003e",
              "self": "\u003cmiddlewared.job.JobsQueue object at 0x7f3cf94cf890\u003e"
            },
            "method": "add"
          },
          {
            "argspec": [
              "self",
              "job"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/job.py",
            "line": "        name = job.get_lock_name()\n",
            "lineno": 157,
            "locals": {
              "job": "\u003cmiddlewared.job.Job object at 0x7f3c0d0cb710\u003e",
              "self": "\u003cmiddlewared.job.JobsQueue object at 0x7f3cf94cf890\u003e"
            },
            "method": "handle_lock"
          },
          {
            "argspec": [
              "self"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/job.py",
            "line": "                raise CallError(\"Error handling job lock. This is most likely caused by invalid call arguments.\",\n",
            "lineno": 376,
            "locals": {
              "lock_name": "\u003cfunction VMService.\u003clambda\u003e at 0x7f3ccd3fdf80\u003e",
              "self": "\u003cmiddlewared.job.Job object at 0x7f3c0d0cb710\u003e"
            },
            "method": "get_lock_name"
          }
        ],
        "repr": "CallError('Error handling job lock. This is most likely caused by invalid call arguments.', 22)"
      }
    },
    "message": "Method call error"
  },
  "id": 8,
  "jsonrpc": "2.0"
}
```

---

### vm.status

**Successful Response:**

```json
{
  "error": {
    "code": -32602,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": [
        [
          "id",
          "Field required",
          22
        ]
      ],
      "reason": "[EINVAL] id: Field required\n",
      "trace": {
        "class": "ValidationErrors",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 731, in _call\n    return await self.run_in_executor(prepared_call.executor, methodobj, *prepared_call.args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 624, in run_in_executor\n    return await loop.run_in_executor(pool, functools.partial(method, *args, **kwargs))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.11/concurrent/futures/thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py\", line 99, in wrapped\n    args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 25, in accept_params\n    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 84, in validate_model\n    raise verrors from None\nmiddlewared.service_exception.ValidationErrors: [EINVAL] id: Field required\n\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_validation_error(id_, sys.exc_info(), list(e))\n",
            "lineno": 335,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "ValidationErrors([ValidationError('id', 'Field required', 22)])",
              "id_": "9",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc90f7c10\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method VMService.status of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc90f7c10\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17c220\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17de40\u003e",
              "method": "'vm.status'",
              "methodobj": "\u003cbound method VMService.status of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "        return await self.run_in_executor(prepared_call.executor, methodobj, *prepared_call.args)\n",
            "lineno": 731,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c0c4da640\u003e}",
              "methodobj": "\u003cbound method VMService.status of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "name": "'vm.status'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          },
          {
            "argspec": [
              "self",
              "pool",
              "method"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "        return await loop.run_in_executor(pool, functools.partial(method, *args, **kwargs))\n",
            "lineno": 624,
            "locals": {
              "args": "()",
              "kwargs": "{}",
              "loop": "\u003c_UnixSelectorEventLoop running=True closed=False debug=False\u003e",
              "method": "\u003cbound method VMService.status of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "pool": "\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e"
            },
            "method": "run_in_executor",
            "varargspec": "args"
          },
          {
            "argspec": [
              "self"
            ],
            "filename": "/usr/lib/python3.11/concurrent/futures/thread.py",
            "line": "            self = None\n",
            "lineno": 62,
            "locals": {
              "self": "None"
            },
            "method": "run"
          },
          {
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py",
            "line": "                args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n",
            "lineno": 99,
            "locals": {
              "accepts": "\u003cclass 'middlewared.api.v25_04_2.vm.VMStatusArgs'\u003e",
              "args": "('***',)",
              "args_index": "1",
              "func": "\u003cfunction VMService.status at 0x7f3ccd4247c0\u003e"
            },
            "method": "wrapped",
            "varargspec": "args"
          },
          {
            "argspec": [
              "model",
              "args",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n",
            "lineno": 25,
            "locals": {
              "args": "()",
              "args_as_dict": "{}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "model": "\u003cclass 'middlewared.api.v25_04_2.vm.VMStatusArgs'\u003e"
            },
            "method": "accept_params"
          },
          {
            "argspec": [
              "model",
              "data",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "        raise verrors from None\n",
            "lineno": 84,
            "locals": {
              "data": "{}",
              "error": "{'type': 'missing', 'loc': ('id',), 'msg': 'Field required', 'input': {}, 'url': 'https://errors.pydantic.dev/2.9/v/missing'}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "loc": "['id']",
              "model": "\u003cclass 'middlewared.api.v25_04_2.vm.VMStatusArgs'\u003e",
              "msg": "'Field required'",
              "verrors": "ValidationErrors([ValidationError('id', 'Field required', 22)])"
            },
            "method": "validate_model"
          }
        ],
        "repr": "ValidationErrors([ValidationError('id', 'Field required', 22)])"
      }
    },
    "message": "Invalid params"
  },
  "id": 9,
  "jsonrpc": "2.0"
}
```

---

### vm.get_instance

**Successful Response:**

```json
{
  "error": {
    "code": -32602,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": [
        [
          "id",
          "Field required",
          22
        ]
      ],
      "reason": "[EINVAL] id: Field required\n",
      "trace": {
        "class": "ValidationErrors",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py\", line 91, in wrapped\n    args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 25, in accept_params\n    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 84, in validate_model\n    raise verrors from None\nmiddlewared.service_exception.ValidationErrors: [EINVAL] id: Field required\n\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_validation_error(id_, sys.exc_info(), list(e))\n",
            "lineno": 335,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "ValidationErrors([ValidationError('id', 'Field required', 22)])",
              "id_": "10",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f410\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.get_instance of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5f410\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17df80\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17ef20\u003e",
              "method": "'vm.get_instance'",
              "methodobj": "\u003cbound method CRUDService.get_instance of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c2c735900\u003e}",
              "methodobj": "\u003cbound method CRUDService.get_instance of \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e\u003e",
              "name": "'vm.get_instance'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.lifecycle.VMService object at 0x7f3cc9388310\u003e, \u003cmiddlewared.plugins.vm.vm_display_info.VMService object at 0x7f3cc9388410\u003e, \u003cmiddlewared.plugins.vm.disk_utils.VMService object at 0x7f3ccd92e550\u003e, \u003cmiddlewared.plugins.vm.memory.VMService object at 0x7f3cc9388850\u003e, \u003cmiddlewared.plugins.vm.vm_lifecycle.VMService object at 0x7f3cc9388890\u003e, \u003cmiddlewared.plugins.vm.vms.VMService object at 0x7f3cc93888d0\u003e, \u003cmiddlewared.plugins.vm.vm_memory_info.VMService object at 0x7f3cc9397810\u003e, \u003cmiddlewared.plugins.vm.clone.VMService object at 0x7f3cc93976d0\u003e, \u003cmiddlewared.plugins.vm.events.VMService object at 0x7f3cc9397e90\u003e, \u003cmiddlewared.plugins.vm.info.VMService object at 0x7f3ccd92de50\u003e, \u003cmiddlewared.plugins.vm.capabilities.VMService object at 0x7f3cc9397e50\u003e, \u003cmiddlewared.plugins.vm.attachments.VMService object at 0x7f3cc9397610\u003e\u003e"
            },
            "method": "_call"
          },
          {
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py",
            "line": "                args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n",
            "lineno": 91,
            "locals": {
              "accepts": "\u003cclass 'middlewared.service.crud_service.VMGetInstanceArgs'\u003e",
              "args": "('***',)",
              "args_index": "1",
              "func": "\u003cfunction CRUDService.get_instance at 0x7f3cf93df7e0\u003e"
            },
            "method": "wrapped",
            "varargspec": "args"
          },
          {
            "argspec": [
              "model",
              "args",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n",
            "lineno": 25,
            "locals": {
              "args": "()",
              "args_as_dict": "{}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "model": "\u003cclass 'middlewared.service.crud_service.VMGetInstanceArgs'\u003e"
            },
            "method": "accept_params"
          },
          {
            "argspec": [
              "model",
              "data",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "        raise verrors from None\n",
            "lineno": 84,
            "locals": {
              "data": "{}",
              "error": "{'type': 'missing', 'loc': ('id',), 'msg': 'Field required', 'input': {}, 'url': 'https://errors.pydantic.dev/2.9/v/missing'}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "loc": "['id']",
              "model": "\u003cclass 'middlewared.service.crud_service.VMGetInstanceArgs'\u003e",
              "msg": "'Field required'",
              "verrors": "ValidationErrors([ValidationError('id', 'Field required', 22)])"
            },
            "method": "validate_model"
          }
        ],
        "repr": "ValidationErrors([ValidationError('id', 'Field required', 22)])"
      }
    },
    "message": "Invalid params"
  },
  "id": 10,
  "jsonrpc": "2.0"
}
```

---

### vm.bootloader_options

**Successful Response:**

```json
{
  "id": 12,
  "jsonrpc": "2.0",
  "result": {
    "UEFI": "UEFI",
    "UEFI_CSM": "Legacy BIOS"
  }
}
```

---

### vm.cpu_model_choices

**Successful Response:**

```json
{
  "id": 14,
  "jsonrpc": "2.0",
  "result": {
    "486": "486",
    "Broadwell": "Broadwell",
    "Broadwell-IBRS": "Broadwell-IBRS",
    "Broadwell-noTSX": "Broadwell-noTSX",
    "Broadwell-noTSX-IBRS": "Broadwell-noTSX-IBRS",
    "Cascadelake-Server": "Cascadelake-Server",
    "Cascadelake-Server-noTSX": "Cascadelake-Server-noTSX",
    "Conroe": "Conroe",
    "Cooperlake": "Cooperlake",
    "Dhyana": "Dhyana",
    "EPYC": "EPYC",
    "EPYC-IBPB": "EPYC-IBPB",
    "EPYC-Milan": "EPYC-Milan",
    "EPYC-Rome": "EPYC-Rome",
    "Haswell": "Haswell",
    "Haswell-IBRS": "Haswell-IBRS",
    "Haswell-noTSX": "Haswell-noTSX",
    "Haswell-noTSX-IBRS": "Haswell-noTSX-IBRS",
    "Icelake-Client": "Icelake-Client",
    "Icelake-Client-noTSX": "Icelake-Client-noTSX",
    "Icelake-Server": "Icelake-Server",
    "Icelake-Server-noTSX": "Icelake-Server-noTSX",
    "IvyBridge": "IvyBridge",
    "IvyBridge-IBRS": "IvyBridge-IBRS",
    "Nehalem": "Nehalem",
    "Nehalem-IBRS": "Nehalem-IBRS",
    "Opteron_G1": "Opteron_G1",
    "Opteron_G2": "Opteron_G2",
    "Opteron_G3": "Opteron_G3",
    "Opteron_G4": "Opteron_G4",
    "Opteron_G5": "Opteron_G5",
    "POWER10": "POWER10",
    "POWER6": "POWER6",
    "POWER7": "POWER7",
    "POWER8": "POWER8",
    "POWER9": "POWER9",
    "POWERPC_e5500": "POWERPC_e5500",
    "POWERPC_e6500": "POWERPC_e6500",
    "Penryn": "Penryn",
    "SandyBridge": "SandyBridge",
    "SandyBridge-IBRS": "SandyBridge-IBRS",
    "Skylake-Client": "Skylake-Client",
    "Skylake-Client-IBRS": "Skylake-Client-IBRS",
    "Skylake-Client-noTSX-IBRS": "Skylake-Client-noTSX-IBRS",
    "Skylake-Server": "Skylake-Server",
    "Skylake-Server-IBRS": "Skylake-Server-IBRS",
    "Skylake-Server-noTSX-IBRS": "Skylake-Server-noTSX-IBRS",
    "Snowridge": "Snowridge",
    "Westmere": "Westmere",
    "Westmere-IBRS": "Westmere-IBRS",
    "athlon": "athlon",
    "core2duo": "core2duo",
    "coreduo": "coreduo",
    "cpu64-rhel5": "cpu64-rhel5",
    "cpu64-rhel6": "cpu64-rhel6",
    "kvm32": "kvm32",
    "kvm64": "kvm64",
    "n270": "n270",
    "pentium": "pentium",
    "pentium2": "pentium2",
    "pentium3": "pentium3",
    "pentiumpro": "pentiumpro",
    "phenom": "phenom",
    "qemu32": "qemu32",
    "qemu64": "qemu64"
  }
}
```

---

### vm.random_mac

**Successful Response:**

```json
{
  "id": 15,
  "jsonrpc": "2.0",
  "result": "00:a0:98:46:e5:92"
}
```

---

### vm.resolution_choices

**Successful Response:**

```json
{
  "id": 17,
  "jsonrpc": "2.0",
  "result": {
    "1024x768": "1024x768",
    "1280x1024": "1280x1024",
    "1280x720": "1280x720",
    "1400x1050": "1400x1050",
    "1600x1200": "1600x1200",
    "1600x900": "1600x900",
    "1920x1080": "1920x1080",
    "1920x1200": "1920x1200",
    "640x480": "640x480",
    "800x600": "800x600"
  }
}
```

---

### vm.get_available_memory

**Successful Response:**

```json
{
  "id": 18,
  "jsonrpc": "2.0",
  "result": 288112946907
}
```

---

### vm.maximum_supported_vcpus

**Successful Response:**

```json
{
  "id": 19,
  "jsonrpc": "2.0",
  "result": 255
}
```

---

### vm.device.query

**Successful Response:**

```json
{
  "error": {
    "code": -32602,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": [
        [
          "filters",
          "Input should be a valid list",
          22
        ]
      ],
      "reason": "[EINVAL] filters: Input should be a valid list\n",
      "trace": {
        "class": "ValidationErrors",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py\", line 91, in wrapped\n    args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 25, in accept_params\n    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py\", line 84, in validate_model\n    raise verrors from None\nmiddlewared.service_exception.ValidationErrors: [EINVAL] filters: Input should be a valid list\n\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_validation_error(id_, sys.exc_info(), list(e))\n",
            "lineno": 335,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "ValidationErrors([ValidationError('filters', 'Input should be a valid list', 22)])",
              "id_": "21",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5c790\u003e",
              "params": "[{}]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.query of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "mock": "None",
              "params": "[{}]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5c790\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17dd00\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17dda0\u003e",
              "method": "'vm.device.query'",
              "methodobj": "\u003cbound method CRUDService.query of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "params": "[{}]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c2c517440\u003e}",
              "methodobj": "\u003cbound method CRUDService.query of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "name": "'vm.device.query'",
              "params": "[{}]",
              "prepared_call": "PreparedCall(args=[{}], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e"
            },
            "method": "_call"
          },
          {
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/decorator.py",
            "line": "                args = list(args[:args_index]) + accept_params(accepts, args[args_index:])\n",
            "lineno": 91,
            "locals": {
              "accepts": "\u003cclass 'middlewared.api.v25_04_2.common.QueryArgs'\u003e",
              "args": "('***', '***')",
              "args_index": "1",
              "func": "\u003cfunction CRUDService.query at 0x7f3cf93df1a0\u003e"
            },
            "method": "wrapped",
            "varargspec": "args"
          },
          {
            "argspec": [
              "model",
              "args",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "    dump = validate_model(model, args_as_dict, exclude_unset=exclude_unset, expose_secrets=expose_secrets)\n",
            "lineno": 25,
            "locals": {
              "args": "({},)",
              "args_as_dict": "{'filters': {}}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "model": "\u003cclass 'middlewared.api.v25_04_2.common.QueryArgs'\u003e"
            },
            "method": "accept_params"
          },
          {
            "argspec": [
              "model",
              "data",
              "exclude_unset",
              "expose_secrets"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/handler/accept.py",
            "line": "        raise verrors from None\n",
            "lineno": 84,
            "locals": {
              "data": "{'filters': {}}",
              "error": "{'type': 'list_type', 'loc': ('filters',), 'msg': 'Input should be a valid list', 'input': {}, 'url': 'https://errors.pydantic.dev/2.9/v/list_type'}",
              "exclude_unset": "False",
              "expose_secrets": "True",
              "loc": "['filters']",
              "model": "\u003cclass 'middlewared.api.v25_04_2.common.QueryArgs'\u003e",
              "msg": "'Input should be a valid list'",
              "verrors": "ValidationErrors([ValidationError('filters', 'Input should be a valid list', 22)])"
            },
            "method": "validate_model"
          }
        ],
        "repr": "ValidationErrors([ValidationError('filters', 'Input should be a valid list', 22)])"
      }
    },
    "message": "Invalid params"
  },
  "id": 21,
  "jsonrpc": "2.0"
}
```

**Working Parameters:**

```json
{}
```

---

### vm.device.create

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.create() missing 1 required positional argument: 'data'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.create() missing 1 required positional argument: 'data'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")",
              "extra": "None",
              "id_": "22",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f6c0d0\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f6c0d0\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17f4c0\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17cd60\u003e",
              "method": "'vm.device.create'",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c0e68f540\u003e}",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "name": "'vm.device.create'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3c0e68f540\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 22,
  "jsonrpc": "2.0"
}
```

---

### vm.device.update

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\")",
              "extra": "None",
              "id_": "23",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f6c910\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.update of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f6c910\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17fc40\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17e700\u003e",
              "method": "'vm.device.update'",
              "methodobj": "\u003cbound method CRUDService.update of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3ccf23ffc0\u003e}",
              "methodobj": "\u003cbound method CRUDService.update of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "name": "'vm.device.update'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3ccf23ffc0\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.update() missing 2 required positional arguments: 'id_' and 'data'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 23,
  "jsonrpc": "2.0"
}
```

---

### vm.device.delete

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.delete() missing 1 required positional argument: 'id_'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.delete() missing 1 required positional argument: 'id_'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")",
              "extra": "None",
              "id_": "24",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5c090\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc8f5c090\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17ec00\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17cb80\u003e",
              "method": "'vm.device.delete'",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c8c20fa00\u003e}",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e\u003e",
              "name": "'vm.device.delete'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3c8c20fa00\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.vm.pci.VMDeviceService object at 0x7f3cc93bde50\u003e, \u003cmiddlewared.plugins.vm.vm_devices.VMDeviceService object at 0x7f3cc93bde90\u003e, \u003cmiddlewared.plugins.vm.usb.VMDeviceService object at 0x7f3cc93bed50\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 24,
  "jsonrpc": "2.0"
}
```

---

### vm.device.disk_choices

**Successful Response:**

```json
{
  "id": 26,
  "jsonrpc": "2.0",
  "result": {
    "/dev/zvol/flashstor/VM/k8s_0-boot": "flashstor/VM/k8s_0-boot",
    "/dev/zvol/flashstor/VM/k8s_0-ebs": "flashstor/VM/k8s_0-ebs",
    "/dev/zvol/flashstor/VM/k8s_0-rook": "flashstor/VM/k8s_0-rook",
    "/dev/zvol/flashstor/VM/k8s_1-boot": "flashstor/VM/k8s_1-boot",
    "/dev/zvol/flashstor/VM/k8s_1-ebs": "flashstor/VM/k8s_1-ebs",
    "/dev/zvol/flashstor/VM/k8s_1-rook": "flashstor/VM/k8s_1-rook",
    "/dev/zvol/flashstor/VM/k8s_2-boot": "flashstor/VM/k8s_2-boot",
    "/dev/zvol/flashstor/VM/k8s_2-ebs": "flashstor/VM/k8s_2-ebs",
    "/dev/zvol/flashstor/VM/k8s_2-rook": "flashstor/VM/k8s_2-rook"
  }
}
```

---

### vm.device.nic_attach_choices

**Successful Response:**

```json
{
  "id": 28,
  "jsonrpc": "2.0",
  "result": {
    "br0": "br0",
    "ens802f0": "ens802f0",
    "ens802f1": "ens802f1"
  }
}
```

---

### vm.device.bind_choices

**Successful Response:**

```json
{
  "id": 30,
  "jsonrpc": "2.0",
  "result": {
    "0.0.0.0": "0.0.0.0",
    "127.0.0.1": "127.0.0.1",
    "192.168.120.10": "192.168.120.10",
    "192.168.120.4": "192.168.120.4",
    "192.168.123.150": "192.168.123.150",
    "192.168.123.151": "192.168.123.151",
    "192.168.123.152": "192.168.123.152",
    "::": "::",
    "::1": "::1"
  }
}
```

---

### vm.device.iotype_choices

**Successful Response:**

```json
{
  "id": 32,
  "jsonrpc": "2.0",
  "result": {
    "IO_URING": "IO_URING",
    "NATIVE": "NATIVE",
    "THREADS": "THREADS"
  }
}
```

---

### pool.dataset.query

**Successful Response:**

```json
{
  "error": {
    "code": -32602,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": [
        [
          "query-filters",
          "Not a list",
          22
        ]
      ],
      "reason": "[EINVAL] query-filters: Not a list\n",
      "trace": {
        "class": "ValidationErrors",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 731, in _call\n    return await self.run_in_executor(prepared_call.executor, methodobj, *prepared_call.args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 624, in run_in_executor\n    return await loop.run_in_executor(pool, functools.partial(method, *args, **kwargs))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.11/concurrent/futures/thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/schema/processor.py\", line 52, in nf\n    res = f(*args, **kwargs)\n          ^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/schema/processor.py\", line 177, in nf\n    args, kwargs = clean_and_validate_args(args, kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/schema/processor.py\", line 167, in clean_and_validate_args\n    verrors.check()\n  File \"/usr/lib/python3/dist-packages/middlewared/service_exception.py\", line 72, in check\n    raise self\nmiddlewared.service_exception.ValidationErrors: [EINVAL] query-filters: Not a list\n\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_validation_error(id_, sys.exc_info(), list(e))\n",
            "lineno": 335,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "ValidationErrors([ValidationError('query-filters', 'Not a list', 22)])",
              "id_": "34",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc9552a50\u003e",
              "params": "[{}]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method returns.\u003clocals\u003e.returns_internal.\u003clocals\u003e.nf of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "mock": "None",
              "params": "[{}]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc9552a50\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17e5c0\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17dd00\u003e",
              "method": "'pool.dataset.query'",
              "methodobj": "\u003cbound method returns.\u003clocals\u003e.returns_internal.\u003clocals\u003e.nf of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "params": "[{}]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.pool_.unlock.PoolDatasetService object at 0x7f3cc94cfe10\u003e, \u003cmiddlewared.plugins.pool_.dataset_info.PoolDatasetService object at 0x7f3cc94cfe50\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_info.PoolDatasetService object at 0x7f3cc94cfed0\u003e, \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e, \u003cmiddlewared.plugins.pool_.dataset_processes.PoolDatasetService object at 0x7f3cc94d4090\u003e, \u003cmiddlewared.plugins.pool_.dataset_quota.PoolDatasetService object at 0x7f3cc94d4250\u003e, \u003cmiddlewared.plugins.pool_.dataset_recordsize.PoolDatasetService object at 0x7f3cc94d4290\u003e, \u003cmiddlewared.plugins.pool_.dataset_attachments.PoolDatasetService object at 0x7f3cc94d42d0\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_operations.PoolDatasetService object at 0x7f3cc94d4310\u003e, \u003cmiddlewared.plugins.pool_.dataset_details.PoolDatasetService object at 0x7f3cc94d4210\u003e, \u003cmiddlewared.plugins.pool_.snapshot_count.PoolDatasetService object at 0x7f3cc94d4350\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_lock.PoolDatasetService object at 0x7f3cc94d4390\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "        return await self.run_in_executor(prepared_call.executor, methodobj, *prepared_call.args)\n",
            "lineno": 731,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c8c76ad00\u003e}",
              "methodobj": "\u003cbound method returns.\u003clocals\u003e.returns_internal.\u003clocals\u003e.nf of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "name": "'pool.dataset.query'",
              "params": "[{}]",
              "prepared_call": "PreparedCall(args=[{}], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.pool_.unlock.PoolDatasetService object at 0x7f3cc94cfe10\u003e, \u003cmiddlewared.plugins.pool_.dataset_info.PoolDatasetService object at 0x7f3cc94cfe50\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_info.PoolDatasetService object at 0x7f3cc94cfed0\u003e, \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e, \u003cmiddlewared.plugins.pool_.dataset_processes.PoolDatasetService object at 0x7f3cc94d4090\u003e, \u003cmiddlewared.plugins.pool_.dataset_quota.PoolDatasetService object at 0x7f3cc94d4250\u003e, \u003cmiddlewared.plugins.pool_.dataset_recordsize.PoolDatasetService object at 0x7f3cc94d4290\u003e, \u003cmiddlewared.plugins.pool_.dataset_attachments.PoolDatasetService object at 0x7f3cc94d42d0\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_operations.PoolDatasetService object at 0x7f3cc94d4310\u003e, \u003cmiddlewared.plugins.pool_.dataset_details.PoolDatasetService object at 0x7f3cc94d4210\u003e, \u003cmiddlewared.plugins.pool_.snapshot_count.PoolDatasetService object at 0x7f3cc94d4350\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_lock.PoolDatasetService object at 0x7f3cc94d4390\u003e\u003e"
            },
            "method": "_call"
          },
          {
            "argspec": [
              "self",
              "pool",
              "method"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "        return await loop.run_in_executor(pool, functools.partial(method, *args, **kwargs))\n",
            "lineno": 624,
            "locals": {
              "args": "('***',)",
              "kwargs": "{}",
              "loop": "\u003c_UnixSelectorEventLoop running=True closed=False debug=False\u003e",
              "method": "\u003cbound method returns.\u003clocals\u003e.returns_internal.\u003clocals\u003e.nf of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "pool": "\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e"
            },
            "method": "run_in_executor",
            "varargspec": "args"
          },
          {
            "argspec": [
              "self"
            ],
            "filename": "/usr/lib/python3.11/concurrent/futures/thread.py",
            "line": "            self = None\n",
            "lineno": 62,
            "locals": {
              "self": "None"
            },
            "method": "run"
          },
          {
            "filename": "/usr/lib/python3/dist-packages/middlewared/schema/processor.py",
            "keywordspec": "kwargs",
            "line": "                res = f(*args, **kwargs)\n",
            "lineno": 52,
            "locals": {
              "args": "('***', '***')",
              "f": "\u003cfunction accepts.\u003clocals\u003e.wrap.\u003clocals\u003e.nf at 0x7f3cca7f9f80\u003e",
              "kwargs": "{}"
            },
            "method": "nf",
            "varargspec": "args"
          },
          {
            "filename": "/usr/lib/python3/dist-packages/middlewared/schema/processor.py",
            "keywordspec": "kwargs",
            "line": "                args, kwargs = clean_and_validate_args(args, kwargs)\n",
            "lineno": 177,
            "locals": {
              "args": "('***', '***')",
              "clean_and_validate_args": "\u003cfunction accepts.\u003clocals\u003e.wrap.\u003clocals\u003e.clean_and_validate_args at 0x7f3cca7f9ee0\u003e",
              "func": "\u003cfunction PoolDatasetService.query at 0x7f3cca7f9d00\u003e",
              "kwargs": "{}"
            },
            "method": "nf",
            "varargspec": "args"
          },
          {
            "argspec": [
              "args",
              "kwargs"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/schema/processor.py",
            "line": "            verrors.check()\n",
            "lineno": 167,
            "locals": {
              "_": "{}",
              "args": "[\u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e, None]",
              "args_index": "1",
              "attr": "\u003cmiddlewared.schema.dict_schema.Dict object at 0x7f3cc8d68110\u003e",
              "common_args": "[\u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e]",
              "deprecated": "[]",
              "f": "\u003cfunction PoolDatasetService.query at 0x7f3cca7f9d00\u003e",
              "had_warning": "False",
              "i": "2",
              "kwarg": "'options'",
              "kwargs": "{'options': {'relationships': True, 'extend': None, 'extend_context': None, 'prefix': None, 'extra': {}, 'order_by': [], 'select': [], 'count': False, 'get': False, 'offset': 0, 'limit': 0, 'force_sql_filters': False}}",
              "nf": "\u003cfunction accepts.\u003clocals\u003e.wrap.\u003clocals\u003e.nf at 0x7f3cca7f9f80\u003e",
              "signature_args": "[{}]",
              "value": "\u003cobject object at 0x7f3d04b85990\u003e",
              "verrors": "ValidationErrors([ValidationError('query-filters', 'Not a list', 22)])",
              "x": "2"
            },
            "method": "clean_and_validate_args"
          },
          {
            "argspec": [
              "self"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/service_exception.py",
            "line": "            raise self\n",
            "lineno": 72,
            "locals": {
              "self": "ValidationErrors([ValidationError('query-filters', 'Not a list', 22)])"
            },
            "method": "check"
          }
        ],
        "repr": "ValidationErrors([ValidationError('query-filters', 'Not a list', 22)])"
      }
    },
    "message": "Invalid params"
  },
  "id": 34,
  "jsonrpc": "2.0"
}
```

**Working Parameters:**

```json
{}
```

---

### pool.dataset.create

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.create() missing 1 required positional argument: 'data'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.create() missing 1 required positional argument: 'data'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")",
              "extra": "None",
              "id_": "35",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc9553a10\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc9553a10\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c17fa60\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17cd60\u003e",
              "method": "'pool.dataset.create'",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.pool_.unlock.PoolDatasetService object at 0x7f3cc94cfe10\u003e, \u003cmiddlewared.plugins.pool_.dataset_info.PoolDatasetService object at 0x7f3cc94cfe50\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_info.PoolDatasetService object at 0x7f3cc94cfed0\u003e, \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e, \u003cmiddlewared.plugins.pool_.dataset_processes.PoolDatasetService object at 0x7f3cc94d4090\u003e, \u003cmiddlewared.plugins.pool_.dataset_quota.PoolDatasetService object at 0x7f3cc94d4250\u003e, \u003cmiddlewared.plugins.pool_.dataset_recordsize.PoolDatasetService object at 0x7f3cc94d4290\u003e, \u003cmiddlewared.plugins.pool_.dataset_attachments.PoolDatasetService object at 0x7f3cc94d42d0\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_operations.PoolDatasetService object at 0x7f3cc94d4310\u003e, \u003cmiddlewared.plugins.pool_.dataset_details.PoolDatasetService object at 0x7f3cc94d4210\u003e, \u003cmiddlewared.plugins.pool_.snapshot_count.PoolDatasetService object at 0x7f3cc94d4350\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_lock.PoolDatasetService object at 0x7f3cc94d4390\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3c4c36ebc0\u003e}",
              "methodobj": "\u003cbound method CRUDService.create of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "name": "'pool.dataset.create'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3c4c36ebc0\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.pool_.unlock.PoolDatasetService object at 0x7f3cc94cfe10\u003e, \u003cmiddlewared.plugins.pool_.dataset_info.PoolDatasetService object at 0x7f3cc94cfe50\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_info.PoolDatasetService object at 0x7f3cc94cfed0\u003e, \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e, \u003cmiddlewared.plugins.pool_.dataset_processes.PoolDatasetService object at 0x7f3cc94d4090\u003e, \u003cmiddlewared.plugins.pool_.dataset_quota.PoolDatasetService object at 0x7f3cc94d4250\u003e, \u003cmiddlewared.plugins.pool_.dataset_recordsize.PoolDatasetService object at 0x7f3cc94d4290\u003e, \u003cmiddlewared.plugins.pool_.dataset_attachments.PoolDatasetService object at 0x7f3cc94d42d0\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_operations.PoolDatasetService object at 0x7f3cc94d4310\u003e, \u003cmiddlewared.plugins.pool_.dataset_details.PoolDatasetService object at 0x7f3cc94d4210\u003e, \u003cmiddlewared.plugins.pool_.snapshot_count.PoolDatasetService object at 0x7f3cc94d4350\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_lock.PoolDatasetService object at 0x7f3cc94d4390\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.create() missing 1 required positional argument: 'data'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 35,
  "jsonrpc": "2.0"
}
```

---

### pool.dataset.delete

**Successful Response:**

```json
{
  "error": {
    "code": -32001,
    "data": {
      "errname": "EINVAL",
      "error": 22,
      "extra": null,
      "reason": "CRUDService.delete() missing 1 required positional argument: 'id_'",
      "trace": {
        "class": "TypeError",
        "formatted": "Traceback (most recent call last):\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py\", line 323, in process_method_call\n    result = await method.call(app, params)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py\", line 52, in call\n    result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 911, in call_with_audit\n    result = await self._call(method, serviceobj, methodobj, params, app=app,\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3/dist-packages/middlewared/main.py\", line 720, in _call\n    return await methodobj(*prepared_call.args)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: CRUDService.delete() missing 1 required positional argument: 'id_'\n",
        "frames": [
          {
            "argspec": [
              "self",
              "app",
              "id_",
              "method",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/ws_handler/rpc.py",
            "line": "                app.send_truenas_error(id_, JSONRPCError.TRUENAS_CALL_ERROR.value, \"Method call error\", errno_,\n",
            "lineno": 353,
            "locals": {
              "adapted": "None",
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "e": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")",
              "errno_": "22",
              "error": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")",
              "extra": "None",
              "id_": "36",
              "method": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc95535d0\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketHandler object at 0x7f3cc8768790\u003e"
            },
            "method": "process_method_call"
          },
          {
            "argspec": [
              "self",
              "app",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/api/base/server/method.py",
            "line": "        result = await self.middleware.call_with_audit(self.name, self.serviceobj, methodobj, params, app)\n",
            "lineno": 52,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "mock": "None",
              "params": "[]",
              "self": "\u003cmiddlewared.api.base.server.method.Method object at 0x7f3cc95535d0\u003e"
            },
            "method": "call"
          },
          {
            "argspec": [
              "self",
              "method",
              "serviceobj",
              "methodobj",
              "params",
              "app"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "                await log_audit_message_for_method(success)\n",
            "lineno": 922,
            "locals": {
              "app": "\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e",
              "audit_callback_messages": "[]",
              "job": "None",
              "job_on_finish_cb": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.job_on_finish_cb at 0x7f3c6c574720\u003e",
              "kwargs": "{}",
              "log_audit_message_for_method": "\u003cfunction Middleware.call_with_audit.\u003clocals\u003e.log_audit_message_for_method at 0x7f3c6c17efc0\u003e",
              "method": "'pool.dataset.delete'",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "params": "[]",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.pool_.unlock.PoolDatasetService object at 0x7f3cc94cfe10\u003e, \u003cmiddlewared.plugins.pool_.dataset_info.PoolDatasetService object at 0x7f3cc94cfe50\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_info.PoolDatasetService object at 0x7f3cc94cfed0\u003e, \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e, \u003cmiddlewared.plugins.pool_.dataset_processes.PoolDatasetService object at 0x7f3cc94d4090\u003e, \u003cmiddlewared.plugins.pool_.dataset_quota.PoolDatasetService object at 0x7f3cc94d4250\u003e, \u003cmiddlewared.plugins.pool_.dataset_recordsize.PoolDatasetService object at 0x7f3cc94d4290\u003e, \u003cmiddlewared.plugins.pool_.dataset_attachments.PoolDatasetService object at 0x7f3cc94d42d0\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_operations.PoolDatasetService object at 0x7f3cc94d4310\u003e, \u003cmiddlewared.plugins.pool_.dataset_details.PoolDatasetService object at 0x7f3cc94d4210\u003e, \u003cmiddlewared.plugins.pool_.snapshot_count.PoolDatasetService object at 0x7f3cc94d4350\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_lock.PoolDatasetService object at 0x7f3cc94d4390\u003e\u003e",
              "success": "False"
            },
            "method": "call_with_audit"
          },
          {
            "argspec": [
              "self",
              "name",
              "serviceobj",
              "methodobj",
              "params"
            ],
            "filename": "/usr/lib/python3/dist-packages/middlewared/main.py",
            "keywordspec": "kwargs",
            "line": "            return await methodobj(*prepared_call.args)\n",
            "lineno": 720,
            "locals": {
              "kwargs": "{'app': \u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, 'audit_callback': \u003cbuilt-in method append of list object at 0x7f3cc7065980\u003e}",
              "methodobj": "\u003cbound method CRUDService.delete of \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e\u003e",
              "name": "'pool.dataset.delete'",
              "params": "[]",
              "prepared_call": "PreparedCall(args=[\u003cmiddlewared.api.base.server.ws_handler.rpc.RpcWebSocketApp object at 0x7f3c0fe978d0\u003e, \u003cbuilt-in method append of list object at 0x7f3cc7065980\u003e], executor=\u003cmiddlewared.utils.threading.IoThreadPoolExecutor object at 0x7f3d03341650\u003e, job=None)",
              "self": "\u003cmiddlewared.main.Middleware object at 0x7f3cf94c7a50\u003e",
              "serviceobj": "\u003cCompoundService: \u003cmiddlewared.plugins.pool_.unlock.PoolDatasetService object at 0x7f3cc94cfe10\u003e, \u003cmiddlewared.plugins.pool_.dataset_info.PoolDatasetService object at 0x7f3cc94cfe50\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_info.PoolDatasetService object at 0x7f3cc94cfed0\u003e, \u003cmiddlewared.plugins.pool_.dataset.PoolDatasetService object at 0x7f3cc94cff10\u003e, \u003cmiddlewared.plugins.pool_.dataset_processes.PoolDatasetService object at 0x7f3cc94d4090\u003e, \u003cmiddlewared.plugins.pool_.dataset_quota.PoolDatasetService object at 0x7f3cc94d4250\u003e, \u003cmiddlewared.plugins.pool_.dataset_recordsize.PoolDatasetService object at 0x7f3cc94d4290\u003e, \u003cmiddlewared.plugins.pool_.dataset_attachments.PoolDatasetService object at 0x7f3cc94d42d0\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_operations.PoolDatasetService object at 0x7f3cc94d4310\u003e, \u003cmiddlewared.plugins.pool_.dataset_details.PoolDatasetService object at 0x7f3cc94d4210\u003e, \u003cmiddlewared.plugins.pool_.snapshot_count.PoolDatasetService object at 0x7f3cc94d4350\u003e, \u003cmiddlewared.plugins.pool_.dataset_encryption_lock.PoolDatasetService object at 0x7f3cc94d4390\u003e\u003e"
            },
            "method": "_call"
          }
        ],
        "repr": "TypeError(\"CRUDService.delete() missing 1 required positional argument: 'id_'\")"
      }
    },
    "message": "Method call error"
  },
  "id": 36,
  "jsonrpc": "2.0"
}
```

---

