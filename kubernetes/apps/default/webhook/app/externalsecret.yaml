---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: webhook
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: webhook-secret
    template:
      data:
        JELLYSEERR_PUSHOVER_URL: pover://{{ .PUSHOVER_USER_KEY }}@{{ .JELLYSEERR_PUSHOVER_TOKEN }}
        RADARR_PUSHOVER_URL: pover://{{ .PUSHOVER_USER_KEY }}@{{ .RADARR_PUSHOVER_TOKEN }}
        SONARR_PUSHOVER_URL: pover://{{ .PUSHOVER_USER_KEY }}@{{ .SONARR_PUSHOVER_TOKEN }}
        SONARR_API_KEY: "{{ .SONARR_API_KEY }}"
  dataFrom:
    - extract:
        key: jellyseerr
    - extract:
        key: radarr
    - extract:
        key: sonarr
    - extract:
        key: pushover
