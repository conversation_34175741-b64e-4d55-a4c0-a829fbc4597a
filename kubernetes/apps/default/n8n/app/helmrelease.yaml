---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: n8n
spec:
  interval: 10m
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      n8n:
        annotations:
          reloader.stakater.com/auto: "true"
        pod:
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
            fsGroupChangePolicy: "OnRootMismatch"
        containers:
          app:
            image:
              repository: ghcr.io/n8n-io/n8n
              tag: 1.108.1
            env:
              DB_SQLITE_VACUUM_ON_STARTUP: true
              EXECUTIONS_DATA_PRUNE: true
              EXECUTIONS_DATA_MAX_AGE: 7
              EXECUTIONS_DATA_PRUNE_MAX_COUNT: 50000
              GENERIC_TIMEZONE: "America/New_York"
              N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS: false
              N8N_PROTOCOL: "https"
              N8N_PORT: &port 8080
              N8N_HOST: &hostName "{{ .Release.Name }}.${SECRET_DOMAIN}"
              N8N_EDITOR_BASE_URL: "https://{{ .Release.Name }}.${SECRET_DOMAIN}"
              WEBHOOK_URL: "https://{{ .Release.Name }}-webhook.${SECRET_DOMAIN}"
              N8N_LOG_LEVEL: info
              N8N_LOG_OUTPUT: console
              N8N_ENCRYPTION_KEY:
                valueFrom:
                  secretKeyRef:
                    name: n8n-secret
                    key: N8N_ENCRYPTION_KEY
            resources:
              requests:
                cpu: 5m
                memory: 128Mi
              limits:
                memory: 2048Mi
    service:
      app:
        ports:
          http:
            port: *port
    persistence:
      data:
        existingClaim: "{{ .Release.Name }}"
        advancedMounts:
          n8n:
            app:
              - path: /home/<USER>/.n8n
    route:
      app:
        hostnames:
          - *hostName
        parentRefs:
          - name: n8n-gateway
            namespace: default
            sectionName: https
      webhooks:
        hostnames:
          - "{{ .Release.Name }}-webhook.${SECRET_DOMAIN}"
        parentRefs:
          - name: external
            namespace: kube-system
            sectionName: https
        rules:
          - matches:
              - path:
                  value: /webhook
            backendRefs: [{}]
          - matches:
              - path:
                  type: PathPrefix
                  value: /form
            backendRefs: [{}]
