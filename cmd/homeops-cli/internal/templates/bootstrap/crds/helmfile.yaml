---
# yaml-language-server: $schema=https://json.schemastore.org/helmfile

# This helmfile is for installing Custom Resource Definitions (CRDs) from Helm charts.
# It is not intended to be used with helmfile apply or sync.

helmDefaults:
  args: ['--include-crds', '--no-hooks'] # Prevent helmfile apply or sync

releases:
  - name: cloudflare-dns
    namespace: network
    chart: oci://ghcr.io/home-operations/charts-mirror/external-dns
    version: 1.18.0

  - name: external-secrets
    namespace: external-secrets
    chart: oci://ghcr.io/external-secrets/charts/external-secrets
    version: 0.19.2

  - name: gateway-api-crds
    namespace: kube-system
    chart: oci://ghcr.io/wiremind/wiremind-helm-charts/gateway-api-crds
    version: 1.3.0

  - name: keda
    namespace: observability
    chart: oci://ghcr.io/home-operations/charts-mirror/keda
    version: 2.17.2

  - name: kube-prometheus-stack
    namespace: observability
    chart: oci://ghcr.io/prometheus-community/charts/kube-prometheus-stack
    version: 76.5.1
