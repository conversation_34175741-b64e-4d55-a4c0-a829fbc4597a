---
name: Flux Local

on:
  pull_request:
    branches: ["main"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  filter:
    name: Flux Local - Filter
    runs-on: ubuntu-latest
    outputs:
      changed-files: ${{ steps.changed-files.outputs.changed_files }}
    steps:
      - name: Get Changed Files
        id: changed-files
        uses: bjw-s-labs/action-changed-files@930cef8463348e168cab7235c47fe95a7a235f65 # v0.3.3
        with:
          patterns: kubernetes/**/*

  test:
    if: ${{ needs.filter.outputs.changed-files != '[]' }}
    needs: filter
    name: Flux Local - Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          persist-credentials: false

      - name: Run flux-local test
        uses: docker://ghcr.io/allenporter/flux-local:v7.8.0
        with:
          args: >-
            test
            --all-namespaces
            --enable-helm
            --path /github/workspace/kubernetes/flux/cluster
            --verbose

  diff:
    if: ${{ needs.filter.outputs.changed-files != '[]' }}
    needs: filter
    name: Flux Local - Diff
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    strategy:
      matrix:
        resource: ["helmrelease", "kustomization"]
      fail-fast: false
    steps:
      - name: Checkout Pull Request Branch
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          path: pull
          persist-credentials: false

      - name: Checkout Default Branch
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          path: default
          persist-credentials: false
          ref: "${{ github.event.repository.default_branch }}"

      - name: Run flux-local diff
        uses: docker://ghcr.io/allenporter/flux-local:v7.8.0
        with:
          args: >-
            diff ${{ matrix.resource }}
            --unified 6
            --path /github/workspace/pull/kubernetes/flux/cluster
            --path-orig /github/workspace/default/kubernetes/flux/cluster
            --strip-attrs "helm.sh/chart,checksum/config,app.kubernetes.io/version,chart"
            --limit-bytes 10000
            --all-namespaces
            --sources "flux-system"
            --output-file diff.patch

      - name: Generate Diff
        id: diff
        run: |
          echo 'diff<<EOF' >> $GITHUB_OUTPUT
          cat diff.patch >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

      - if: ${{ steps.diff.outputs.diff != '' }}
        name: Generate Token
        uses: actions/create-github-app-token@a8d616148505b5069dccd32f177bb87d7f39123b # v2.1.1
        id: app-token
        with:
          app-id: ${{ secrets.BOT_APP_ID }}
          private-key: ${{ secrets.BOT_APP_PRIVATE_KEY }}

      - if: ${{ steps.diff.outputs.diff != '' }}
        name: Add Comment
        uses: marocchino/sticky-pull-request-comment@773744901bac0e8cbb5a0dc842800d45e9b2b405 # v2.9.4
        with:
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
          header: ${{ github.event.pull_request.number }}/kubernetes/${{ matrix.resource }}
          message: |
            ```diff
            ${{ steps.diff.outputs.diff }}
            ```

  success:
    if: ${{ !cancelled() }}
    needs: ["test", "diff"]
    name: Flux Local - Success
    runs-on: ubuntu-latest
    steps:
      - name: Any jobs failed?
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1

      - name: All jobs passed or skipped?
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: echo "All jobs passed or skipped" && echo "${{ toJSON(needs.*.result) }}"
