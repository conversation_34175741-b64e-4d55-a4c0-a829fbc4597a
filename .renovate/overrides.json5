{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  packageRules: [
    {
      description: "Override Helmfile Dependency Name",
      matchDatasources: ["docker"],
      matchManagers: ["helmfile"],
      overrideDepName: "{{packageName}}",
    },
    {
      description: "Override Talos Installer Package Name",
      matchDatasources: ["docker"],
      matchPackageNames: ["/factory\\.talos\\.dev/"],
      overridePackageName: "ghcr.io/siderolabs/installer",
    },
  ],
}
