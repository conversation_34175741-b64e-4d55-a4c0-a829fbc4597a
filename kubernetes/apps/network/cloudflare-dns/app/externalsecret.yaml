---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: cloudflare-dns
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: cloudflare-dns-secret
    template:
      data:
        CF_API_TOKEN: "{{ .CLOUDFLARE_DNS_TOKEN }}"
        CF_ZONE_ID: "{{ .CLOUDFLARE_ZONE_ID }}"
  dataFrom:
    - extract:
        key: cloudflare
