{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  packageRules: [
    {
      description: "Actions Runner Controller Group",
      groupName: "actions-runner-controller",
      matchDatasources: ["docker"],
      matchPackageNames: ["/gha-runner-scale-set-controller/", "/gha-runner-scale-set/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
      minimumGroupSize: 2,
    },
    {
      description: "Flux Operator Group",
      groupName: "flux-operator",
      matchDatasources: ["docker"],
      matchPackageNames: ["/flux-operator/", "/flux-instance/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
      minimumGroupSize: 2,
    },
    {
      description: "Kubernetes Group",
      groupName: "kubernetes",
      matchDatasources: ["docker"],
      matchPackageNames: [
        "/kube-apiserver/",
        "/kube-controller-manager/",
        "/kube-scheduler/",
        "/kubelet/",
      ],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
      minimumGroupSize: 4,
    },
    {
      description: "Rook-Ceph Group",
      groupName: "rook-ceph",
      matchDatasources: ["docker"],
      matchPackageNames: ["/rook-ceph/", "/rook-ceph-cluster/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
      minimumGroupSize: 2,
    },
    {
      description: "Talos Group",
      groupName: "talos",
      matchDatasources: ["docker"],
      matchPackageNames: ["/metal-installer/", "/talosctl/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
      minimumGroupSize: 2,
    },
  ],
}
