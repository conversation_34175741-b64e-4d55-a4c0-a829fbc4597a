---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: autobrr
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: autobrr-secret
    template:
      data:
        AUTOBRR__SESSION_SECRET: "{{ .AUTOBRR_SESSION_SECRET }}"
  dataFrom:
    - extract:
        key: autobrr
