---
name: Image Pull

on:
  pull_request:
    branches: ["main"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  filter:
    name: Image Pull - Filter
    runs-on: ubuntu-latest
    outputs:
      changed-files: ${{ steps.changed-files.outputs.changed_files }}
    steps:
      - name: Get Changed Files
        id: changed-files
        uses: bjw-s-labs/action-changed-files@930cef8463348e168cab7235c47fe95a7a235f65 # v0.3.3
        with:
          patterns: kubernetes/**/*

  extract:
    if: ${{ needs.filter.outputs.changed-files != '[]' }}
    needs: filter
    name: Image Pull - Extract Images
    runs-on: ubuntu-latest
    strategy:
      matrix:
        branch: ["default", "pull"]
      fail-fast: false
    outputs:
      default: ${{ steps.extract.outputs.default }}
      pull: ${{ steps.extract.outputs.pull }}
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          persist-credentials: false
          ref: ${{ matrix.branch == 'default' && github.event.repository.default_branch || '' }}

      - name: Gather Images
        uses: docker://ghcr.io/allenporter/flux-local:v7.8.0
        with:
          args: >-
            get cluster
            --all-namespaces
            --path /github/workspace/kubernetes/flux/cluster
            --enable-images
            --only-images
            --output json
            --output-file images.json

      - name: Extract Images
        id: extract
        run: |
          echo "${{ matrix.branch }}=$(jq --compact-output '.' images.json)" >> $GITHUB_OUTPUT

          echo '## Branch ${{ matrix.branch }} images' >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          jq '.' images.json >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY

  diff:
    if: ${{ needs.extract.outputs.default != needs.extract.outputs.pull }}
    needs: extract
    name: Image Pull - Diff Images
    runs-on: ubuntu-latest
    outputs:
      images: ${{ steps.diff.outputs.images }}
    steps:
      - name: Diff Images
        id: diff
        run: |
          images=$(jq --compact-output --null-input \
              --argjson f1 '${{ needs.extract.outputs.default }}' \
              --argjson f2 '${{ needs.extract.outputs.pull }}' \
              '$f2 - $f1' \
          )
          echo "images=${images}" >> $GITHUB_OUTPUT

          echo '## New images to Pull' >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          echo $images | jq >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY

  pull:
    if: ${{ needs.diff.outputs.images != '[]' }}
    needs: diff
    name: Image Pull - Pull Images
    runs-on: home-ops-runner
    strategy:
      matrix:
        image: ${{ fromJSON(needs.diff.outputs.images) }}
      max-parallel: 4
      fail-fast: false
    env:
      MATRIX_IMAGE: ${{ matrix.image }}
    steps:
      - name: Install talosctl
        run: curl -fsSL https://talos.dev/install | sh

      - name: Pull Image
        run: talosctl --nodes $NODE image pull "${MATRIX_IMAGE}"

  success:
    if: ${{ !cancelled() }}
    needs: pull
    name: Image Pull - Success
    runs-on: ubuntu-latest
    steps:
      - name: Any jobs failed?
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1

      - name: All jobs passed or skipped?
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: echo "All jobs passed or skipped" && echo "${{ toJSON(needs.*.result) }}"
