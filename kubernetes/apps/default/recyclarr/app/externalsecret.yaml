---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: recyclarr
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: recyclarr-secret
    template:
      data:
        RADARR_API_KEY: "{{ .RADARR_API_KEY }}"
        SONARR_API_KEY: "{{ .SONARR_API_KEY }}"
  dataFrom:
    - extract:
        key: radarr
    - extract:
        key: sonarr
