---
apiVersion: volsync.backube/v1alpha1
kind: ReplicationDestination
metadata:
  name: {{ ENV.APP }}-manual
  namespace: {{ ENV.NS }}
spec:
  trigger:
    manual: restore-once
  kopia:
    accessModes: {{ ENV.ACCESS_MODES }}
    cacheAccessModes: {{ ENV.CACHE_ACCESS_MODES }}
    cacheCapacity: {{ ENV.CACHE_CAPACITY }}
    cacheStorageClassName: {{ ENV.CACHE_STORAGE_CLASS }}
    cleanupCachePVC: true
    cleanupTempPVC: true
    copyMethod: Direct
    destinationPVC: {{ ENV.CLAIM }}
    enableFileDeletion: true
    moverSecurityContext:
      runAsUser: {{ ENV.PUID }}
      runAsGroup: {{ ENV.PGID }}
      fsGroup: {{ ENV.PGID }}
    previous: {{ ENV.PREVIOUS }}
    repository: {{ ENV.APP }}-volsync-secret
    sourceIdentity:
      sourceName: {{ ENV.APP }}
    storageClassName: {{ ENV.STORAGE_CLASS_NAME }}

