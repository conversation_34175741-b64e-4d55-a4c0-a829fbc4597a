---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: generic-device-plugin
spec:
  interval: 30m
  chartRef:
    kind: OCIRepository
    name: app-template
    namespace: flux-system
  driftDetection:
    mode: enabled

  values:
    defaultPodOptions:
      priorityClassName: system-node-critical

    controllers:
      generic-device-plugin:
        type: daemonset
        strategy: RollingUpdate
        containers:
          app:
            image:
              repository: ghcr.io/squat/generic-device-plugin
              tag: 36bfc606bba2064de6ede0ff2764cbb52edff70d@sha256:ba6f0b4cf6c858d6ad29ba4d32e4da11638abbc7d96436bf04f582a97b2b8821
            args:
              - --log-level=info
              - --config=/config/config.yaml
            resources:
              requests:
                cpu: 10m
              limits:
                memory: 64Mi
            securityContext:
              # privileged: true
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities:
                drop:
                  - ALL

    persistence:
      config:
        type: configMap
        name: generic-device-plugin
        globalMounts:
          - path: /config/config.yaml
            subPath: config.yaml
            readOnly: true
      dev:
        type: hostPath
        hostPath: /dev
        globalMounts:
          - readOnly: true
      sys:
        type: hostPath
        hostPath: /sys
        globalMounts:
          - readOnly: true
      var-lib-kubelet-device-plugins:
        type: hostPath
        hostPath: /var/lib/kubelet/device-plugins
