---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: external-secrets
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 0.19.2
  url: oci://ghcr.io/external-secrets/charts/external-secrets
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: external-secrets
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: external-secrets
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    installCRDs: true
    leaderElect: true
    image:
      repository: ghcr.io/external-secrets/external-secrets
    webhook:
      image:
        repository: ghcr.io/external-secrets/external-secrets
      serviceMonitor:
        enabled: true
        interval: 1m
    certController:
      image:
        repository: ghcr.io/external-secrets/external-secrets
      serviceMonitor:
        enabled: true
        interval: 1m
    serviceMonitor:
      enabled: true
      interval: 1m
    grafanaDashboard:
      enabled: true
