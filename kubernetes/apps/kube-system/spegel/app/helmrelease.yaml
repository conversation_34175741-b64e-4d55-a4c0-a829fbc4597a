---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: spegel
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 0.3.0
  url: oci://ghcr.io/spegel-org/helm-charts/spegel
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: spegel
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: spegel
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    spegel:
      containerdSock: /run/containerd/containerd.sock
      containerdRegistryConfigPath: /etc/cri/conf.d/hosts
    service:
      registry:
        hostPort: 29999
    serviceMonitor:
      enabled: true
    grafanaDashboard:
      enabled: true
