---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: n8n
spec:
  refreshInterval: 5m
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: n8n-secret
    creationPolicy: Owner
    template:
      data:
        N8N_ENCRYPTION_KEY: "{{ .N8N_ENCRYPTION_KEY }}"
  dataFrom:
    - extract:
        key: n8n
