---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/volsync.backube/replicationdestination_v1alpha1.json
apiVersion: volsync.backube/v1alpha1
kind: ReplicationDestination
metadata:
  name: "${APP}-dst"
  labels:
    kustomize.toolkit.fluxcd.io/ssa: IfNotPresent
spec:
  trigger:
    manual: restore-once
  kopia:
    accessModes: ["${VOLSYNC_ACCESSMODES:=ReadWriteOnce}"]
    cacheAccessModes: ["${VOLSYNC_CACHE_ACCESSMODES:=ReadWriteOnce}"]
    cacheCapacity: "${VOLSYNC_CAPACITY:=5Gi}"
    cacheStorageClassName: "${VOLSYNC_CACHE_SNAPSHOTCLASS:=openebs-hostpath}"
    capacity: "${VOLSYNC_CAPACITY:=5Gi}"
    cleanupCachePVC: true
    cleanupTempPVC: true
    copyMethod: Snapshot
    enableFileDeletion: true
    moverSecurityContext:
      runAsUser: ${VOLSYNC_PUID:=1000}
      runAsGroup: ${VOLSYNC_PGID:=1000}
      fsGroup: ${VOLSYNC_PGID:=1000}
    repository: ${APP}-volsync-secret
    sourceIdentity:
      sourceName: ${APP}
    storageClassName: "${VOLSYNC_STORAGECLASS:=ceph-block}"
    volumeSnapshotClassName: "${VOLSYNC_SNAPSHOTCLASS:=csi-ceph-blockpool}"
