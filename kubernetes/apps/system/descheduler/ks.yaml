---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: &appname descheduler
  namespace: &namespace system
spec:
  targetNamespace: *namespace
  commonMetadata:
    labels:
      app.kubernetes.io/name: *appname
  path: "./kubernetes/apps/system/descheduler/app"
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  interval: 30m
  timeout: 5m
  prune: true
  wait: false
