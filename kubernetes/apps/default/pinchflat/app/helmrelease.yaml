---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: pinchflat
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      pinchflat:
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: ghcr.io/kieraneglin/pinchflat
              tag: v2025.6.6@sha256:4e975edf58f0861a5cbfe8fc6aac4851ff5a02dfc3f05ffeea4982e3084a5a4a
            env:
              TZ: America/New_York
              TZ_DATA_DIR: /tmp/elixir_tz_data
              PORT: &port 80
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /healthcheck
                    port: *port
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 15m
                memory: 2Gi
              limits:
                memory: 4Gi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
        supplementalGroups: [44]
    service:
      app:
        ports:
          http:
            port: *port
    route:
      app:
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: pinchflat-gateway
            namespace: default
            sectionName: https
    persistence:
      config:
        existingClaim: pinchflat
      media:
        type: nfs
        server: nas01.${SECRET_DOMAIN}
        path: /mnt/flashstor/data
        advancedMounts:
          pinchflat:
            app:
              - path: /downloads
                subPath: Library/YouTube
      tmpfs:
        type: emptyDir
        advancedMounts:
          pinchflat:
            app:
              - path: /etc/yt-dlp
                subPath: yt-dlp
              - path: /tmp
                subPath: tmp
