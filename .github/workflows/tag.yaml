---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: Tag

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 1 * *"

permissions:
  contents: read

jobs:
  main:
    name: Tag
    runs-on: ubuntu-latest
    steps:
      - name: Generate Token
        uses: actions/create-github-app-token@a8d616148505b5069dccd32f177bb87d7f39123b # v2.1.1
        id: app-token
        with:
          app-id: "${{ secrets.BOT_APP_ID }}"
          private-key: "${{ secrets.BOT_APP_PRIVATE_KEY }}"

      - name: Get Previous Tag and Determine Next Tag
        id: determine-next-tag
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: "${{ steps.app-token.outputs.token }}"
          result-encoding: string
          script: |
            const { data: tags } = await github.rest.repos.listTags({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 1,
            });

            const prevTag = tags[0]?.name || "0.0.0"; // Default to "0.0.0" if no tags exist
            const [prevMajor, prevMinor, prevPatch] = prevTag.split('.').map(Number);
            const now = new Date();
            const nextMajorMinor = `${now.getFullYear()}.${now.getMonth() + 1}`; // Months are 0-indexed in JavaScript
            const nextPatch = `${prevMajor}.${prevMinor}` === nextMajorMinor ? prevPatch + 1 : 0;

            console.log(`Previous tag: ${prevTag}`);
            console.log(`Next tag: ${nextMajorMinor}.${nextPatch}`);

            return `${nextMajorMinor}.${nextPatch}`;

      - name: Create Tag
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: "${{ steps.app-token.outputs.token }}"
          script: |
            const tagName = "${{ steps.determine-next-tag.outputs.result }}";

            const tag = await github.rest.git.createTag({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag: tagName,
              message: tagName,
              object: context.sha,
              type: "commit"
            });

            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: `refs/tags/${tagName}`,
              sha: tag.data.sha
            });
