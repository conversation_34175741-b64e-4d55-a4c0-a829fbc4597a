---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: sonarr
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: sonarr-secret
    template:
      data:
        SONARR__AUTH__APIKEY: "{{ .SONARR_API_KEY }}"
  dataFrom:
    - extract:
        key: sonarr
