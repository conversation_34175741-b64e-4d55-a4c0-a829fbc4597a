[env]
# File paths
KUBECONFIG = "{{config_root}}/kubeconfig"
MINIJINJA_CONFIG_FILE = "{{config_root}}/.minijinja.toml"
SOPS_AGE_KEY_FILE = "{{config_root}}/age.key"
TALOSCONFIG = "{{config_root}}/talosconfig"
# Krew plugin path
_.path = ["${KREW_ROOT:-$HOME/.krew}/bin"]
# Included file paths
_.file = [
  "{{config_root}}/onepassword.env",
  "{{config_root}}/kubernetes/apps/system-upgrade/versions/versions.env"
]
# _.python.venv = { path = "{{config_root}}/.venv", create = true }

[tools]
#"golangci-lint" = "latest"
# "python" = "3.13"
# "uv" = "latest"
# "pipx:flux-local" = "latest"
