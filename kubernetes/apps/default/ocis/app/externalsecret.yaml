---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: ocis
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: ocis-secret
    template:
      data:
        OCIS_JWT_SECRET: "{{ .OCIS_JWT_SECRET }}"
  dataFrom:
    - extract:
        key: ocis
