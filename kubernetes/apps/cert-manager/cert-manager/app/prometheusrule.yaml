---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/monitoring.coreos.com/prometheusrule_v1.json
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cert-manager-rules
spec:
  groups:
    - name: cert-manager.rules
      rules:
        - alert: CertManagerAbsent
          expr: |
            absent(up{job="cert-manager"})
          for: 5m
          annotations:
            summary: >-
              Cert Manager has dissapeared from Prometheus service discovery
          labels:
            severity: critical

    - name: certificates
      rules:
        - alert: CertManagerCertExpirySoon
          expr: |
            avg by (exported_namespace, namespace, name) (certmanager_certificate_expiration_timestamp_seconds - time()) < (21 * 24 * 3600)
          for: 5m
          annotations:
            summary: >-
              The cert {{ $labels.name }} is {{ $value | humanizeDuration }} from expiry, it should have renewed over a week ago
          labels:
            severity: critical

        - alert: CertManagerCertNotReady
          expr: |
            max by (name, exported_namespace, namespace, condition) (certmanager_certificate_ready_status{condition!="True"} == 1)
          for: 5m
          annotations:
            summary: >-
              The cert {{ $labels.name }} is not ready to serve traffic
          labels:
            severity: critical

        - alert: CertManagerHittingRateLimits
          expr: |
            sum by (host) (rate(certmanager_http_acme_client_request_count{status="429"}[5m])) > 0
          for: 5m
          annotations:
            summary: >-
              Cert manager hitting LetsEncrypt rate limits
          labels:
            severity: critical
