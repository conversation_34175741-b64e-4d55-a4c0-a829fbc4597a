---
# yaml-language-server: $schema=https://json.schemastore.org/helmfile

helmDefaults:
  cleanupOnFail: true
  wait: true
  waitForJobs: true

releases:
  - name: cilium
    namespace: kube-system
    chart: oci://ghcr.io/home-operations/charts-mirror/cilium
    version: 1.18.1
    values: ['./values.yaml.gotmpl']

  - name: coredns
    namespace: kube-system
    chart: oci://ghcr.io/coredns/charts/coredns
    version: 1.43.2
    values: ['./values.yaml.gotmpl']
    needs: ['kube-system/cilium']

  - name: spegel
    namespace: kube-system
    chart: oci://ghcr.io/spegel-org/helm-charts/spegel
    version: 0.3.0
    values: ['./values.yaml.gotmpl']
    needs: ['kube-system/coredns']

  - name: cert-manager
    namespace: cert-manager
    chart: oci://quay.io/jetstack/charts/cert-manager
    version: v1.18.2
    values: ['./values.yaml.gotmpl']
    needs: ['kube-system/spegel']

  - name: flux-operator
    namespace: flux-system
    chart: oci://ghcr.io/controlplaneio-fluxcd/charts/flux-operator
    version: 0.28.0
    values: ['./values.yaml.gotmpl']
    needs: ['cert-manager/cert-manager']

  - name: flux-instance
    namespace: flux-system
    chart: oci://ghcr.io/controlplaneio-fluxcd/charts/flux-instance
    version: 0.28.0
    values: ['./values.yaml.gotmpl']
    needs: ['flux-system/flux-operator']
