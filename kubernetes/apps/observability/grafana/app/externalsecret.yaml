---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: grafana-admin
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: grafana-admin-secret
    template:
      data:
        admin-user: "{{ .GRAFANA_ADMIN_USERNAME }}"
        admin-password: "{{ .GRAFANA_ADMIN_PASSWORD }}"
  dataFrom:
    - extract:
        key: grafana
