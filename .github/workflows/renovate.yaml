---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: Renovate

on:
  push:
    branches: ["main"]
    paths:
      - .renovaterc.json5
      - .renovate/**.json5
  schedule:
    - cron: "0 * * * *"
  workflow_dispatch:
    inputs:
      dryRun:
        description: Dry Run
        type: boolean
        default: false
        required: true
      logLevel:
        description: Log Level
        type: choice
        default: debug
        options:
          - debug
          - info
        required: true
      version:
        description: Renovate Version
        default: latest
        required: true

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  main:
    name: Renovate
    runs-on: ubuntu-latest
    permissions:
      packages: read
    steps:
      - name: Generate Token
        uses: actions/create-github-app-token@a8d616148505b5069dccd32f177bb87d7f39123b # v2.1.1
        id: app-token
        with:
          app-id: "${{ secrets.BOT_APP_ID }}"
          private-key: "${{ secrets.BOT_APP_PRIVATE_KEY }}"

      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          persist-credentials: false
          token: "${{ steps.app-token.outputs.token }}"

      - name: Run Renovate
        uses: renovatebot/github-action@b11417b9eaac3145fe9a8544cee66503724e32b6 # v43.0.8
        env:
          LOG_LEVEL: "${{ inputs.logLevel || 'debug' }}"
          RENOVATE_AUTODISCOVER: true
          RENOVATE_AUTODISCOVER_FILTER: "${{ github.repository }}"
          RENOVATE_DRY_RUN: "${{ inputs.dryRun }}"
          RENOVATE_INTERNAL_CHECKS_FILTER: strict
          RENOVATE_PLATFORM: github
          RENOVATE_PLATFORM_COMMIT: true
        with:
          token: "${{ steps.app-token.outputs.token }}"
          renovate-version: "${{ inputs.version || 'latest' }}"
