---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: alloy
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 1.2.1
  url: oci://ghcr.io/home-operations/charts-mirror/alloy
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: alloy
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: alloy
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    fullnameOverride: alloy
    alloy:
      configMap:
        create: false
        name: &name alloy-configmap
        key: config.alloy
    controller:
      podAnnotations:
        configmap.reloader.stakater.com/reload: *name
    serviceMonitor:
      enabled: true
