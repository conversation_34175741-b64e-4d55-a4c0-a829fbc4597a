---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/notification.toolkit.fluxcd.io/alert_v1beta3.json
apiVersion: notification.toolkit.fluxcd.io/v1beta3
kind: Alert
metadata:
  name: alertmanager
spec:
  providerRef:
    name: alertmanager
  eventSeverity: error
  eventSources:
    - kind: FluxInstance
      name: "*"
    - kind: GitRepository
      name: "*"
    - kind: HelmRelease
      name: "*"
    - kind: HelmRepository
      name: "*"
    - kind: Kustomization
      name: "*"
    - kind: OCIRepository
      name: "*"
  exclusionList:
    - "error.*lookup github\\.com"
    - "error.*lookup raw\\.githubusercontent\\.com"
    - "dial.*tcp.*timeout"
    - "waiting.*socket"
  suspend: false
