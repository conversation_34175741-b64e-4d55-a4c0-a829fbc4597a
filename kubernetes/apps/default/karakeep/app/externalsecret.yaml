---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: karakeep
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: karakeep-secret
    template:
      data:
        encryption_key: "{{ .KARAKEEP_ENCRYPTION_KEY }}"
        meilisearch_master_key: "{{ .KARAKEEP_MEILISEARCH_MASTER_KEY }}"
  dataFrom:
    - extract:
        key: karakeep
