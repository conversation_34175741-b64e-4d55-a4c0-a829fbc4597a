---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: coredns
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 1.43.2
  url: oci://ghcr.io/coredns/charts/coredns
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: coredns
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: coredns
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    fullnameOverride: coredns
    image:
      repository: mirror.gcr.io/coredns/coredns
    replicaCount: 2
    k8sAppLabelOverride: kube-dns
    serviceAccount:
      create: true
    service:
      name: kube-dns
      clusterIP: **********
    servers:
      - zones:
          - zone: .
            scheme: dns://
            use_tcp: true
        port: 53
        plugins:
          - name: errors
          - name: health
            configBlock: |-
              lameduck 5s
          - name: ready
          - name: kubernetes
            parameters: cluster.local in-addr.arpa ip6.arpa
            configBlock: |-
              pods verified
              fallthrough in-addr.arpa ip6.arpa
          - name: autopath
            parameters: "@kubernetes"
          - name: forward
            parameters: . /etc/resolv.conf
          - name: cache
            configBlock: |-
              prefetch 20
              serve_stale
          - name: loop
          - name: reload
          - name: loadbalance
          - name: prometheus
            parameters: 0.0.0.0:9153
          - name: log
            configBlock: |-
              class error
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: node-role.kubernetes.io/control-plane
                  operator: Exists
    tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - key: node-role.kubernetes.io/control-plane
        operator: Exists
        effect: NoSchedule
