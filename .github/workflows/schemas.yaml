---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: Schemas

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *"
  push:
    branches: ["main"]
    paths: [".github/workflows/schemas.yaml"]

env:
  UV_SYSTEM_PYTHON: "1"

permissions:
  contents: read

jobs:
  main:
    name: <PERSON>hem<PERSON>
    runs-on: home-ops-runner
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          persist-credentials: false

      - name: Install kubectl
        uses: azure/setup-kubectl@776406bce94f63e41d621b960d78ee25c8b76ede # v4.0.1

      - name: Install uv
        uses: astral-sh/setup-uv@d9e0f98d3fc6adb07d1e3d37f3043649ddad06a1 # v6.5.0

      - name: Setup Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.13.x

      - name: Setup Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Install Python Dependencies
        run: uv pip install pyyaml

      - name: Run crd-extractor
        run: curl -fsSL https://raw.githubusercontent.com/datreeio/CRDs-catalog/43e4407642d4c37683c88711f37caa6c9c20ca40/Utilities/crd-extractor.sh | bash

      - name: Publish Schemas
        uses: cloudflare/wrangler-action@**************************************** # v3.14.1
        with:
          apiToken: "${{ secrets.CLOUDFLARE_API_TOKEN }}"
          accountId: "${{ secrets.CLOUDFLARE_ACCOUNT_ID }}"
          workingDirectory: /home/<USER>/.datree/crdSchemas
          command: pages deploy --project-name=kubernetes-schema --branch main .
