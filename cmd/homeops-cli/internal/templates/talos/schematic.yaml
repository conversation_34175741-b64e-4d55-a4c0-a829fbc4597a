---
customization:
  extraKernelArgs:
    - -init_on_alloc                      # Less security, faster puter
    - -init_on_free                       # Less security, faster puter
    - -selinux                            # Less security, faster puter
    - apparmor=0                          # Less security, faster puter
    - init_on_alloc=0                     # Less security, faster puter
    - init_on_free=0                      # Less security, faster puter
    - mitigations=off                     # Less security, faster puter
    - security=none                       # Less security, faster puter
    - talos.auditd.disabled=1             # Less security, faster puter
  systemExtensions:
    officialExtensions:
      - siderolabs/nfsrahead              # NFS Performance
