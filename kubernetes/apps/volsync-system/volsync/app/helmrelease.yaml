---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/helmrepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: HelmRepository
metadata:
  name: volsync
  namespace: volsync-system
spec:
  interval: 1h
  url: https://perfectra1n.github.io/volsync/charts/
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: volsync
spec:
  interval: 1h
  chart:
    spec:
      chart: volsync
      version: 0.16.7
      sourceRef:
        kind: HelmRepository
        name: volsync
        namespace: volsync-system
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    image: &image
      repository: ghcr.io/perfectra1n/volsync
      tag: v0.15.38
    kopia: *image
    rclone: *image
    restic: *image
    rsync: *image
    rsync-tls: *image
    syncthing: *image
    manageCRDs: true
    metrics:
      disableAuth: true
    podSecurityContext:
      runAsNonRoot: true
      runAsUser: 1000
      runAsGroup: 1000
