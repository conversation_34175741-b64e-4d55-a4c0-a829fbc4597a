---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: grafana
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 9.3.4
  url: oci://ghcr.io/grafana/helm-charts/grafana
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: grafana
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: grafana
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    deploymentStrategy:
      type: Recreate
    admin:
      existingSecret: grafana-admin-secret
    env:
      GF_DATE_FORMATS_USE_BROWSER_LOCALE: true
      GF_EXPLORE_ENABLED: true
      GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS: natel-discrete-panel,pr0ps-trackmap-panel,panodata-map-panel
      GF_SECURITY_ANGULAR_SUPPORT_ENABLED: true
      GF_SERVER_ROOT_URL: https://grafana.${SECRET_DOMAIN}
    grafana.ini:
      analytics:
        check_for_updates: false
        check_for_plugin_updates: false
        reporting_enabled: false
      auth.anonymous:
        enabled: true
        org_id: 1
        org_name: Main Org.
        org_role: Viewer
      news:
        news_feed_enabled: false
    datasources:
      datasources.yaml:
        apiVersion: 1
        deleteDatasources:
          - { name: Alertmanager, orgId: 1 }
          - { name: Loki, orgId: 1 }
          - { name: Prometheus, orgId: 1 }
        datasources:
          - name: Prometheus
            type: prometheus
            uid: prometheus
            access: proxy
            url: http://prometheus-operated.observability.svc.cluster.local:9090
            isDefault: true
          - name: Loki
            type: loki
            uid: loki
            access: proxy
            url: http://loki-headless.observability.svc.cluster.local:3100
            jsonData:
              maxLines: 250
          - name: Alertmanager
            type: alertmanager
            uid: alertmanager
            access: proxy
            url: http://alertmanager-operated.observability.svc.cluster.local:9093
            jsonData:
              implementation: prometheus
    dashboardProviders:
      dashboardproviders.yaml:
        apiVersion: 1
        providers:
          - name: default
            orgId: 1
            folder: ""
            type: file
            disableDeletion: false
            editable: true
            options:
              path: /var/lib/grafana/dashboards/default
    dashboards:
      default:
        ceph-cluster:
          # renovate: depName="Ceph Cluster"
          gnetId: 2842
          revision: 18
          datasource: Prometheus
        ceph-osd:
          # renovate: depName="Ceph - OSD (Single)"
          gnetId: 5336
          revision: 9
          datasource: Prometheus
        ceph-pools:
          # renovate: depName="Ceph - Pools"
          gnetId: 5342
          revision: 9
          datasource: Prometheus
        cert-manager:
          # renovate: depName="Cert-manager-Kubernetes"
          gnetId: 20842
          revision: 3
          datasource: Prometheus
        cloudflared:
          # renovate: depName="Cloudflare Tunnels (cloudflared)"
          gnetId: 17457
          revision: 6
          datasource:
            - { name: DS_PROMETHEUS, value: Prometheus }
        envoy:
           # renovate: depName="Envoy Proxy Monitoring gRPC"
           gnetId: 23239
           revision: 1
           datasource:
             - { name: DS_AFRANET_PROMETHEUS, value: Prometheus }
        external-dns:
          # renovate: depName="External-dns"
          gnetId: 15038
          revision: 3
          datasource: Prometheus
        kubernetes-api-server:
          # renovate: depName="Kubernetes / System / API Server"
          gnetId: 15761
          revision: 19
          datasource: Prometheus
        kubernetes-coredns:
          # renovate: depName="Kubernetes / System / CoreDNS"
          gnetId: 15762
          revision: 21
          datasource: Prometheus
        kubernetes-global:
          # renovate: depName="Kubernetes / Views / Global"
          gnetId: 15757
          revision: 43
          datasource: Prometheus
        kubernetes-namespaces:
          # renovate: depName="Kubernetes / Views / Namespaces"
          gnetId: 15758
          revision: 42
          datasource: Prometheus
        kubernetes-nodes:
          # renovate: depName="Kubernetes / Views / Nodes"
          gnetId: 15759
          revision: 37
          datasource: Prometheus
        kubernetes-pods:
          # renovate: depName="Kubernetes / Views / Pods"
          gnetId: 15760
          revision: 36
          datasource: Prometheus
        kubernetes-volumes:
          # renovate: depName="K8s / Storage / Volumes / Cluster"
          gnetId: 11454
          revision: 14
          datasource: Prometheus
        node-exporter-full:
          # renovate: depName="Node Exporter Full"
          gnetId: 1860
          revision: 41
          datasource:
            - { name: DS_PROMETHEUS, value: Prometheus }
        prometheus:
          # renovate: depName="Prometheus"
          gnetId: 19105
          revision: 7
          datasource: Prometheus
        volsync:
          # renovate: depName="VolSync Dashboard"
          gnetId: 21356
          revision: 3
          datasource:
            - { name: DS_PROMETHEUS, value: Prometheus }
            - { name: VAR_REPLICATIONDESTNAME, value: .*-dst }
        smartctl-exporter:
          # renovate: depName="SMARTctl Exporter Dashboard"
          gnetId: 22604
          revision: 2
          datasource:
            - { name: DS_PROMETHEUS, value: Prometheus }
        zfs:
          # renovate: depName="ZFS"
          gnetId: 7845
          revision: 4
          datasource: Prometheus
    sidecar:
      image:
        registry: ghcr.io
        repository: home-operations/k8s-sidecar
        tag: 1.30.9@sha256:74d65c3def9276b24b5bfe41f8efb773174e7a1ecf3c9b5a31bd02cfdee232c9
      dashboards:
        enabled: true
        searchNamespace: ALL
        label: grafana_dashboard
        folderAnnotation: grafana_folder
        provider:
          disableDelete: true
          foldersFromFilesStructure: true
      datasources:
        enabled: true
        searchNamespace: ALL
        labelValue: ""
    plugins:
      - grafana-clock-panel
      - grafana-piechart-panel
      - grafana-worldmap-panel
      - natel-discrete-panel
      - pr0ps-trackmap-panel
      - vonage-status-panel
    serviceMonitor:
      enabled: true
    route:
      main:
        enabled: true
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: grafana-gateway
            namespace: observability
            sectionName: https
    persistence:
      enabled: false
    testFramework:
      enabled: false
