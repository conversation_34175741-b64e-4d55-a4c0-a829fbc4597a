---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: actual
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      actual:
        annotations:
          reloader.stakater.com/auto: "true"
        pod:
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
            fsGroupChangePolicy: "OnRootMismatch"
        containers:
          app:
            image:
              repository: ghcr.io/actualbudget/actual-server
              tag: 25.8.0
            env:
              ACTUAL_PORT: &httpPort 5006
            probes:
              liveness:
                enabled: true
              readiness:
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /
                    port: *httpPort
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 12m
                memory: 128Mi
              limits:
                memory: 512Mi
    service:
      app:
        ports:
          http:
            port: *httpPort
    persistence:
      config:
        existingClaim: "{{ .Release.Name }}"
        advancedMounts:
          actual:
            app:
              - path: /data
    route:
      app:
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: actual-gateway
            namespace: default
            sectionName: https
