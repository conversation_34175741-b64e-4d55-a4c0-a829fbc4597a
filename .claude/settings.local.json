{"permissions": {"allow": ["Bash(kubectl describe:*)", "Bash(kubectl logs:*)", "Bash(kubectl get events:*)", "Bash(kubectl get pods:*)", "<PERSON><PERSON>(kube<PERSON>l get secret:*)", "Bash(kubectl get:*)", "Bash(kubectl --kubeconfig=./kubeconfig get cronjobs -A)", "Bash(kubectl:*)", "Bash(gh api:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(golangci-lint run:*)", "Bash(grep:*)", "Bash(rg:*)", "WebFetch(domain:raw.githubusercontent.com)", "WebSearch", "WebFetch(domain:github.com)"], "deny": [], "ask": []}}