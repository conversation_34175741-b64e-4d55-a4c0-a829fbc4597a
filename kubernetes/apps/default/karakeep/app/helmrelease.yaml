---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: karakeep
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      karakeep:
        forceRename: karakeep
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: ghcr.io/karakeep-app/karakeep
              tag: 0.26.0
            env:
              BROWSER_WEB_URL: http://{{ .Release.Name }}-chrome.{{ .Release.Namespace }}.svc.cluster.local:9222
              CRAWLER_DOWNLOAD_BANNER_IMAGE: true
              CRAWLER_ENABLE_ADBLOCKER: true
              CRAWLER_STORE_SCREENSHOT: true
              DATA_DIR: /data
              DISABLE_SIGNUPS: true
              MEILI_ADDR: http://{{ .Release.Name }}-meilisearch.{{ .Release.Namespace }}.svc.cluster.local:7700
              MEILI_MASTER_KEY:
                valueFrom:
                  secretKeyRef:
                    name: karakeep-secret
                    key: meilisearch_master_key
              NEXTAUTH_SECRET:
                valueFrom:
                  secretKeyRef:
                    name: karakeep-secret
                    key: encryption_key
              NEXTAUTH_URL: https://{{ .Release.Name }}.${SECRET_DOMAIN}
              DISABLE_NEW_RELEASE_CHECK: true
              COREPACK_INTEGRITY_KEYS: 0
            probes:
              liveness:
                enabled: true
              readiness:
                enabled: true
            resources:
              requests:
                cpu: 10m
                memory: 128Mi
              limits:
                memory: 1Gi
      chrome:
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: gcr.io/zenika-hub/alpine-chrome
              tag: 124
            command:
              - chromium-browser
            args:
              - --headless
              - --no-sandbox
              - --disable-gpu
              - --disable-dev-shm-usage
              - --remote-debugging-address=0.0.0.0
              - --remote-debugging-port=9222
              - --hide-scrollbars
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: false
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 10m
                memory: 128Mi
              limits:
                memory: 1Gi
      meilisearch:
        annotations:
          reloader.stakater.com/auto: "true"
        pod:
          affinity:
            podAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                - labelSelector:
                    matchExpressions:
                      - key: app.kubernetes.io/name
                        operator: In
                        values:
                          - karakeep
                      - key: app.kubernetes.io/controller
                        operator: In
                        values:
                          - karakeep
                  topologyKey: kubernetes.io/hostname
        containers:
          app:
            image:
              repository: docker.io/getmeili/meilisearch
              tag: v1.18.0
            args:
              - /bin/meilisearch
              - --experimental-dumpless-upgrade
            env:
              MEILI_NO_ANALYTICS: true
              MEILI_MASTER_KEY:
                valueFrom:
                  secretKeyRef:
                    name: karakeep-secret
                    key: meilisearch_master_key
            resources:
              requests:
                cpu: 10m
                memory: 128Mi
              limits:
                memory: 4Gi
    defaultPodOptions:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    service:
      app:
        forceRename: karakeep
        primary: true
        controller: karakeep
        ports:
          http:
            port: &httpPort 3000
      chrome:
        controller: chrome
        ports:
          http:
            port: 9222
      meilisearch:
        controller: meilisearch
        ports:
          http:
            port: 7700
    persistence:
      config:
        existingClaim: "{{ .Release.Name }}"
        advancedMounts:
          karakeep:
            app:
              - path: /data
                subPath: karakeep
          meilisearch:
            app:
              - path: /meili_data
                subPath: meilisearch
    route:
      app:
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: karakeep-gateway
            namespace: default
            sectionName: https
        rules:
          - backendRefs:
              - identifier: app
                port: *httpPort
