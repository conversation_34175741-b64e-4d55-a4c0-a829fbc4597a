{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  customManagers: [
    {
      customType: "regex",
      description: "Process annotated dependencies",
      managerFilePatterns: [
        "/\\.env(?:\\.j2)?$/",
        "/\\.sh(?:\\.j2)?$/",
        "/\\.yaml(?:\\.j2)?$/",
      ],
      matchStrings: [
        // # renovate: datasource=github-releases depName=k3s-io/k3s
        // k3s_release_version: &version v1.29.0+k3s1
        // # renovate: datasource=helm depName=cilium repository=https://helm.cilium.io
        // version: 1.15.1
        // # renovate: datasource=docker depName=ghcr.io/siderolabs/kubelet
        // KUBERNETES_VERSION=v1.31.1
        "datasource=(?<datasource>\\S+) depName=(?<depName>\\S+)( repository=(?<registryUrl>\\S+))?\\n.+(:\\s|=)(&\\S+\\s)?(?<currentValue>\\S+)",
        // # renovate: datasource=docker depName=ghcr.io/prometheus-operator/prometheus-operator
        // https://raw.githubusercontent.com/prometheus-operator/prometheus-operator/v0.80.0/example/prometheus-operator-crd/monitoring.coreos.com_alertmanagerconfigs.yaml
        "datasource=(?<datasource>\\S+) depName=(?<depName>\\S+)\\n.+/(?<currentValue>(v|\\d)[^/]+)",
      ],
      datasourceTemplate: "{{#if datasource}}{{{datasource}}}{{else}}github-releases{{/if}}",
    },
  ],
}
