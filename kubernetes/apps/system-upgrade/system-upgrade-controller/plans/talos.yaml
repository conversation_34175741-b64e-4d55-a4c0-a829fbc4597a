---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/upgrade.cattle.io/plan_v1.json
apiVersion: upgrade.cattle.io/v1
kind: Plan
metadata:
  name: talos
spec:
  # renovate: datasource=docker depName=ghcr.io/siderolabs/installer
  version: v1.11.0-rc.0
  concurrency: 1
  postCompleteDelay: 2m
  exclusive: true
  serviceAccountName: system-upgrade-controller
  secrets:
    - name: system-upgrade-controller
      path: /var/run/secrets/talos.dev
      ignoreUpdates: true
  nodeSelector:
    matchExpressions:
      - key: kubernetes.io/hostname
        operator: Exists
  upgrade:
    image: ghcr.io/jfroy/tnu:0.4.3
    args:
      - --node=$(SYSTEM_UPGRADE_NODE_NAME)
      - --tag=$(SYSTEM_UPGRADE_PLAN_LATEST_VERSION)
      - --powercycle