---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: rook-ceph-dashboard
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: rook-ceph-dashboard-password # rook-ceph expects this name
    template:
      data:
        password: "{{ .ROOK_DASHBOARD_PASSWORD }}"
  dataFrom:
    - extract:
        key: rook-ceph
