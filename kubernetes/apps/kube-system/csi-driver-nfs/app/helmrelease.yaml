---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: csi-driver-nfs
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 4.11.0
  url: oci://ghcr.io/home-operations/charts-mirror/csi-driver-nfs
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: csi-driver-nfs
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: csi-driver-nfs
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    storageClass:
      create: true
      name: nfs-slow
      parameters:
        server: **************
        share: /mnt/flashstor/kubernetes
      mountOptions:
        - nfsvers=4.2
        - nconnect=16
        - hard
        - noatime
      reclaimPolicy: Delete
      volumeBindingMode: Immediate
