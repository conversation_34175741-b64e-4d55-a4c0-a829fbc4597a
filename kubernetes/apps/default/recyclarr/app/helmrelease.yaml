---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: recyclarr
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      recyclarr:
        type: cronjob
        cronjob:
          schedule: 0 0 * * *
          backoffLimit: 0
          concurrencyPolicy: Forbid
          successfulJobsHistory: 1
          failedJobsHistory: 1
          ttlSecondsAfterFinished: 86400
        containers:
          app:
            image:
              repository: ghcr.io/recyclarr/recyclarr
              tag: 7.4.1@sha256:759540877f95453eca8a26c1a93593e783a7a824c324fbd57523deffb67f48e1
            envFrom:
              - secretRef:
                  name: recyclarr-secret
            args: ["sync"]
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 5m
                memory: 36Mi
              limits:
                memory: 128Mi
        pod:
          restartPolicy: Never
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    persistence:
      config:
        existingClaim: recyclarr
      config-file:
        type: configMap
        name: recyclarr-configmap
        globalMounts:
          - path: /config/recyclarr.yml
            subPath: recyclarr.yml
            readOnly: true
      tmpfs:
        type: emptyDir
        advancedMounts:
          recyclarr:
            app:
              - path: /config/logs
                subPath: logs
              - path: /config/repositories
                subPath: repositories
              - path: /tmp
                subPath: tmp
