---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: autobrr
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      autobrr:
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: ghcr.io/autobrr/autobrr
              tag: v1.65.0@sha256:494e821e7a9c9a1279d1541522a65ed06b03d0b66563e827e3d29b9a63e61ddc
            env:
              AUTOBRR__HOST: 0.0.0.0
              AUTOBRR__PORT: &port 80
              AUTOBRR__METRICS_ENABLED: true
              AUTOBRR__METRICS_HOST: 0.0.0.0
              AUTOBRR__METRICS_PORT: &metricsPort 9094
              AUTOBRR__CHECK_FOR_UPDATES: false
              AUTOBRR__LOG_LEVEL: INFO
            envFrom:
              - secretRef:
                  name: "{{ .Release.Name }}-secret"
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /api/healthz/liveness
                    port: *port
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 10m
                memory: 128Mi
              limits:
                memory: 256Mi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    service:
      app:
        ports:
          http:
            port: *port
          metrics:
            port: *metricsPort
    serviceMonitor:
      app:
        endpoints:
          - port: metrics
    route:
      app:
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: autobrr-gateway
            namespace: default
            sectionName: https
    persistence:
      config:
        existingClaim: "{{ .Release.Name }}"
      tmpfs:
        type: emptyDir
        advancedMounts:
          autobrr:
            app:
              - path: /config/log
                subPath: log
              - path: /tmp
                subPath: tmp
