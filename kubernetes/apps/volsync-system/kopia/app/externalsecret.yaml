---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: kopia
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: kopia-secret
    template:
      data:
        KOPIA_PASSWORD: "{{ .KOPIA_PASSWORD }}"
  dataFrom:
    - extract:
        key: volsync-template
