{
  "editor.bracketPairColorization.enabled": true,
  "editor.fontFamily": "FiraCode Nerd Font",
  "editor.fontLigatures": true,
  "editor.guides.bracketPairs": true,
  "editor.guides.bracketPairsHorizontal": true,
  "editor.guides.highlightActiveBracketPair": true,
  "editor.hover.delay": 1500,
  "editor.rulers": [100],
  "editor.stickyScroll.enabled": false,
  "explorer.autoReveal": false,
  "files.associations": {
    "**/*.json5": "json5"
  },
  "files.trimTrailingWhitespace": true,
  "material-icon-theme.files.associations": {
    "*.sops.yaml": "lock",
    "helmfile.yaml": "helm",
    "kubeconfig": "kubernetes",
    "talosconfig": "kubernetes"
  },
  "material-icon-theme.folders.associations": {
    // top level
    ".github/workflows": "ci",
    ".private": "archive",
    ".renovate": "robot",
    "bootstrap": "seeders",
    "flux": "pipe",
    "talos": "linux",
    // namespaces
    "actions-runner-system": "github",
    "cert-manager": "guard",
    "default": "home",
    "external-secrets": "secure",
    "flux-system": "pipe",
    "kube-system": "kubernetes",
    "network": "connection",
    "observability": "event",
    "openebs-system": "base",
    "rook-ceph": "dump",
    "system-upgrade": "update",
    "volsync-system": "aws"
  },
  "sops.defaults.ageKeyFile": "age.key",
  "vs-kubernetes": {
    "vs-kubernetes.kubeconfig": "./kubeconfig",
    "vs-kubernetes.knownKubeconfigs": []
  },
  "yaml.schemaStore.enable": true,
  "yaml.schemas": {
    "kubernetes": "./kubernetes/**/*.yaml"
  }
}
