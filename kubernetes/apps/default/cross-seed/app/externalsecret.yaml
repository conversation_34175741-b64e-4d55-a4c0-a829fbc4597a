---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: cross-seed
spec:
  refreshInterval: 5m
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: cross-seed-secret
    template:
      data:
        config.js: |
          function fetchIndexers(baseUrl, apiKey, tag){
            const buffer = require('child_process').execSync(`curl -fsSL "$${baseUrl}/api/v1/tag/detail?apikey=$${apiKey}"`);
            const response = JSON.parse(buffer.toString('utf8'));
            const indexerIds = response.filter(t => t.label === tag)[0]?.indexerIds ?? [];
            const indexers = indexerIds.map(i => `$${baseUrl}/$${i}/api?apikey=$${apiKey}`);
            console.log(`Loaded $${indexers.length} indexers from Prowlarr`);
            return indexers;
          }
          module.exports = {
            action: "inject",
            apiKey: "{{.CROSS_SEED_API_KEY}}",
            blockList: ["category:manual"],
            linkCategory: "cross-seed",
            linkDirs: ["/media/torrents/complete/cross-seed"],
            linkType: "hardlink",
            matchMode: "partial",
            port: Number(process.env.CROSS_SEED_PORT),
            skipRecheck: true,
            radarr: ["http://radarr.default.svc.cluster.local/?apikey={{ .RADARR_API_KEY }}"],
            sonarr: ["http://sonarr.default.svc.cluster.local/?apikey={{ .SONARR_API_KEY }}"],
            torrentClients: ["qbittorrent:http://qbittorrent.default.svc.cluster.local"],
            torznab: fetchIndexers("http://prowlarr.default.svc.cluster.local", "{{.PROWLARR_API_KEY}}", "cross-seed"),
            useClientTorrents: true
          };
  dataFrom:
    - extract:
        key: cross-seed
    - extract:
        key: prowlarr
    - extract:
        key: radarr
    - extract:
        key: sonarr
