---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: descheduler
spec:
  interval: 30m
  chart:
    spec:
      chart: descheduler
      version: 0.32.2
      sourceRef:
        kind: HelmRepository
        name: descheduler
  values:
    replicas: 1
    kind: Deployment
    deschedulerPolicyAPIVersion: descheduler/v1alpha2
    deschedulerPolicy:
      profiles:
        - name: Default
          pluginConfig:
            - name: DefaultEvictor
              args:
                evictFailedBarePods: true
                evictLocalStoragePods: true
                evictSystemCriticalPods: true
                nodeFit: true
            - name: RemovePodsViolatingInterPodAntiAffinity
            - name: RemovePodsViolatingNodeAffinity
              args:
                nodeAffinityType:
                  - requiredDuringSchedulingIgnoredDuringExecution
            - name: RemovePodsViolatingNodeTaints
            - name: RemovePodsViolatingTopologySpreadConstraint
              args:
                constraints:
                  - DoNotSchedule
                  - ScheduleAnyway
          plugins:
            balance:
              enabled:
                - RemovePodsViolatingTopologySpreadConstraint
            deschedule:
              enabled:
                - RemovePodsViolatingInterPodAntiAffinity
                - RemovePodsViolatingNodeAffinity
                - RemovePodsViolatingNodeTaints
    service:
      enabled: true
    serviceMonitor:
      enabled: true
    leaderElection:
      enabled: true
