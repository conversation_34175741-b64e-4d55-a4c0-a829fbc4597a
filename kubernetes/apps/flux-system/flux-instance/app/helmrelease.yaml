---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: flux-instance
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 0.28.0
  url: oci://ghcr.io/controlplaneio-fluxcd/charts/flux-instance
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: flux-instance
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: flux-instance
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    instance:
      distribution:
        # renovate: datasource=github-releases depName=controlplaneio-fluxcd/distribution
        version: 2.6.4
      cluster:
        networkPolicy: false
      components:
        - source-controller
        - kustomize-controller
        - helm-controller
        - notification-controller
      sync:
        kind: GitRepository
        url: https://github.com/GizmoTickler/home-ops
        ref: refs/heads/main
        path: kubernetes/flux/cluster
        interval: 1h
      commonMetadata:
        labels:
          app.kubernetes.io/name: flux
      kustomize:
        patches:
          - # Increase the number of workers
            patch: |
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --concurrent=10
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --requeue-dependency=5s
            target:
              kind: Deployment
              name: (kustomize-controller|helm-controller|source-controller)
          - # Increase the memory limits
            patch: |
              apiVersion: apps/v1
              kind: Deployment
              metadata:
                name: all
              spec:
                template:
                  spec:
                    containers:
                      - name: manager
                        resources:
                          limits:
                            memory: 2Gi
            target:
              kind: Deployment
              name: (kustomize-controller|helm-controller|source-controller)
          - # Enable in-memory kustomize builds
            patch: |
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --concurrent=20
              - op: replace
                path: /spec/template/spec/volumes/0
                value:
                  name: temp
                  emptyDir:
                    medium: Memory
            target:
              kind: Deployment
              name: kustomize-controller
          - # Enable Helm repositories caching
            patch: |
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --helm-cache-max-size=10
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --helm-cache-ttl=60m
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --helm-cache-purge-interval=5m
            target:
              kind: Deployment
              name: source-controller
          - # Flux near OOM detection for Helm
            patch: |
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --feature-gates=OOMWatch=true
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --oom-watch-memory-threshold=95
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --oom-watch-interval=500ms
            target:
              kind: Deployment
              name: helm-controller
          - # Disable chart digest tracking
            patch: |
              - op: add
                path: /spec/template/spec/containers/0/args/-
                value: --feature-gates=DisableChartDigestTracking=true
            target:
              kind: Deployment
              name: helm-controller
