---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/monitoring.coreos.com/prometheusrule_v1.json
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: external-dns-rules
spec:
  groups:
    - name: external-dns.rules
      rules:
        - alert: ExternalDNSStale
          expr: |
            time() - external_dns_controller_last_sync_timestamp_seconds > 60
          for: 5m
          annotations:
            summary: >-
              ExternalDNS ({{ $labels.job }}) has not synced successfully in the last five minutes
          labels:
            severity: critical
