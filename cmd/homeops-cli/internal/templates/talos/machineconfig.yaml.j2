---
version: v1alpha1
machine:
  ca:
    crt: op://Infrastructure/talos/MACHINE_CA_CRT
    {% if machinetype == 'controlplane' %}
    key: op://Infrastructure/talos/MACHINE_CA_KEY
    {% endif %}
  certSANs:
    - 127.0.0.1
    - ***************
  features:
    apidCheckExtKeyUsage: true
    diskQuotaSupport: true
    hostDNS:
      enabled: true
      forwardKubeDNSToHost: false
      resolveMemberNames: true
    kubePrism:
      enabled: true
      port: 7445
    {% if machinetype == 'controlplane' %}
    kubernetesTalosAPIAccess:
      allowedKubernetesNamespaces:
        - actions-runner-system
        - system-upgrade
      allowedRoles:
        - os:admin
      enabled: true
    {% endif %}
    rbac: true
    stableHostname: true
  files:
    - op: create
      path: /etc/cri/conf.d/20-customization.part
      content: |
        [plugins."io.containerd.cri.v1.images"]
          discard_unpacked_layers = false
        [plugins."io.containerd.cri.v1.runtime"]
          device_ownership_from_security_context = true
    - op: overwrite
      path: /etc/nfsmount.conf
      permissions: 0o644
      content: |
        [ NFSMount_Global_Options ]
        nfsvers=4.2
        hard=True
        nconnect=16
        noatime=True
  install:
    wipe: false
  kernel:
    modules:
      - name: nbd
  kubelet:
    defaultRuntimeSeccompProfileEnabled: true
    disableManifestsDirectory: true
    extraConfig:
      featureGates:
        ImageVolume: true
      serializeImagePulls: false
    image: ghcr.io/siderolabs/kubelet:v1.33.4
    nodeIP:
      validSubnets:
        - *************/22
  nodeLabels:
    topology.kubernetes.io/region: main
  sysctls:
    fs.inotify.max_user_instances: "8192"
    fs.inotify.max_user_watches: "1048576"
    net.core.default_qdisc: fq
    net.core.rmem_max: "67108864"
    net.core.wmem_max: "67108864"
    net.ipv4.neigh.default.gc_thresh1: "4096"
    net.ipv4.neigh.default.gc_thresh2: "8192"
    net.ipv4.neigh.default.gc_thresh3: "16384"
    net.ipv4.tcp_congestion_control: bbr
    net.ipv4.tcp_fastopen: "3"
    net.ipv4.tcp_mtu_probing: "1"
    net.ipv4.tcp_rmem: 4096 87380 33554432
    net.ipv4.tcp_window_scaling: "1"
    net.ipv4.tcp_wmem: 4096 65536 33554432
    sunrpc.tcp_max_slot_table_entries: "128"
    sunrpc.tcp_slot_table_entries: "128"
    user.max_user_namespaces: "11255"
    vm.nr_hugepages: "1024"
  token: op://Infrastructure/talos/MACHINE_TOKEN
cluster:
  ca:
    crt: op://Infrastructure/talos/CLUSTER_CA_CRT
    {% if machinetype == 'controlplane' %}
    key: op://Infrastructure/talos/CLUSTER_CA_KEY
    {% endif %}
  clusterName: main
  controlPlane:
    endpoint: https://***************:6443
  id: op://Infrastructure/talos/CLUSTER_ID
  network:
    cni:
      name: none
    dnsDomain: cluster.local
    podSubnets:
      - *********/16
    serviceSubnets:
      - *********/16
  secret: op://Infrastructure/talos/CLUSTER_SECRET
  token: op://Infrastructure/talos/CLUSTER_TOKEN
  {% if machinetype == 'controlplane' %}
  aggregatorCA:
    crt: op://Infrastructure/talos/CLUSTER_AGGREGATORCA_CRT
    key: op://Infrastructure/talos/CLUSTER_AGGREGATORCA_KEY
  allowSchedulingOnControlPlanes: true
  apiServer:
    auditPolicy:
      apiVersion: audit.k8s.io/v1
      kind: Policy
      rules:
        - level: Metadata
    certSANs:
      - 127.0.0.1
      - ***************
    disablePodSecurityPolicy: true
    extraArgs:
      enable-aggregator-routing: "true"
      feature-gates: ImageVolume=true,MutatingAdmissionPolicy=true
      runtime-config: admissionregistration.k8s.io/v1alpha1=true
    image: registry.k8s.io/kube-apiserver:v1.33.4
  controllerManager:
    extraArgs:
      bind-address: 0.0.0.0
    image: registry.k8s.io/kube-controller-manager:v1.33.4
  coreDNS:
    disabled: true
  etcd:
    advertisedSubnets:
      - *************/22
    ca:
      crt: op://Infrastructure/talos/CLUSTER_ETCD_CA_CRT
      key: op://Infrastructure/talos/CLUSTER_ETCD_CA_KEY
    extraArgs:
      listen-metrics-urls: http://0.0.0.0:2381
  proxy:
    disabled: true
  scheduler:
    config:
      apiVersion: kubescheduler.config.k8s.io/v1
      kind: KubeSchedulerConfiguration
      profiles:
        - schedulerName: default-scheduler
          plugins:
            score:
              disabled:
                - name: ImageLocality
          pluginConfig:
            - name: PodTopologySpread
              args:
                defaultingType: List
                defaultConstraints:
                  - maxSkew: 1
                    topologyKey: kubernetes.io/hostname
                    whenUnsatisfiable: ScheduleAnyway
    extraArgs:
      bind-address: 0.0.0.0
    image: registry.k8s.io/kube-scheduler:v1.33.4
  secretboxEncryptionSecret: op://Infrastructure/talos/CLUSTER_SECRETBOXENCRYPTIONSECRET
  serviceAccount:
    key: op://Infrastructure/talos/CLUSTER_SERVICEACCOUNT_KEY
  {% endif %}