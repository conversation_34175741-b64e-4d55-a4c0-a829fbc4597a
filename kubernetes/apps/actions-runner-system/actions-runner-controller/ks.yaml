---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: &app actions-runner-controller
  namespace: &namespace actions-runner-system
spec:
  commonMetadata:
    labels:
      app.kubernetes.io/name: *app
  healthChecks:
    - apiVersion: helm.toolkit.fluxcd.io/v2
      kind: HelmRelease
      name: *app
      namespace: *namespace
  interval: 1h
  path: ./kubernetes/apps/actions-runner-system/actions-runner-controller/app
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  targetNamespace: *namespace
  timeout: 5m
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: &app actions-runner-controller-runners
  namespace: &namespace actions-runner-system
spec:
  commonMetadata:
    labels:
      app.kubernetes.io/name: *app
  dependsOn:
    - name: actions-runner-controller
      namespace: *namespace
    - name: openebs
      namespace: openebs-system
  interval: 1h
  path: ./kubernetes/apps/actions-runner-system/actions-runner-controller/runners
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  targetNamespace: *namespace
  timeout: 5m
  wait: false
