---
version: v1alpha1
debug: false
persist: true
machine:
  token: op://Infrastructure/talos/MACHINE_TOKEN
  ca:
    crt: op://Infrastructure/talos/MACHINE_CA_CRT
    {% if machinetype == 'controlplane' %}
    key: op://Infrastructure/talos/MACHINE_CA_KEY
    {% endif %}
  certSANs: ["127.0.0.1", "***************"]
  kubelet:
    image: ghcr.io/siderolabs/kubelet:v1.33.4
    extraConfig:
      serializeImagePulls: false
    defaultRuntimeSeccompProfileEnabled: true
    nodeIP:
      validSubnets: ["*************/22"]
    disableManifestsDirectory: true
  kernel:
    modules:
      - name: nbd
  files:
    - # Spegel
      op: create
      path: /etc/cri/conf.d/20-customization.part
      content: |
        [plugins."io.containerd.cri.v1.images"]
          discard_unpacked_layers = false
    - op: overwrite
      path: /etc/nfsmount.conf
      permissions: 0o644
      content: |
        [ NFSMount_Global_Options ]
        nfsvers=4.2
        hard=True
        nconnect=16
        noatime=True
  sysctls:
    fs.inotify.max_user_instances: "8192"
    fs.inotify.max_user_watches: "1048576"
    net.core.default_qdisc: fq
    net.core.rmem_max: "67108864"
    net.core.wmem_max: "67108864"
    net.ipv4.neigh.default.gc_thresh1: "4096"
    net.ipv4.neigh.default.gc_thresh2: "8192"
    net.ipv4.neigh.default.gc_thresh3: "16384"
    net.ipv4.tcp_congestion_control: bbr
    net.ipv4.tcp_fastopen: "3"
    net.ipv4.tcp_mtu_probing: "1"
    net.ipv4.tcp_rmem: 4096 87380 33554432
    net.ipv4.tcp_window_scaling: "1"
    net.ipv4.tcp_wmem: 4096 65536 33554432
    sunrpc.tcp_max_slot_table_entries: "128"
    sunrpc.tcp_slot_table_entries: "128"
    user.max_user_namespaces: "11255"
    vm.nr_hugepages: "1024"
  features:
    rbac: true
    stableHostname: true
    {% if machinetype == 'controlplane' %}
    kubernetesTalosAPIAccess:
      enabled: true
      allowedRoles: ["os:admin"]
      allowedKubernetesNamespaces: ["actions-runner-system", "system-upgrade"]
    {% endif %}
    apidCheckExtKeyUsage: true
    diskQuotaSupport: true
    kubePrism:
      enabled: true
      port: 7445
    hostDNS:
      enabled: true
      resolveMemberNames: true
      forwardKubeDNSToHost: false
cluster:
  id: op://Infrastructure/talos/CLUSTER_ID
  secret: op://Infrastructure/talos/CLUSTER_SECRET
  controlPlane:
    endpoint: https://***************:6443
  clusterName: main
  network:
    cni:
      name: none
    dnsDomain: cluster.local
    podSubnets: ["*********/16"]
    serviceSubnets: ["*********/16"]
  coreDNS:
    disabled: true
  token: op://Infrastructure/talos/CLUSTER_TOKEN
  secretboxEncryptionSecret: op://Infrastructure/talos/CLUSTER_SECRETBOXENCRYPTIONSECRET
  ca:
    crt: op://Infrastructure/talos/CLUSTER_CA_CRT
    {% if machinetype == 'controlplane' %}
    key: op://Infrastructure/talos/CLUSTER_CA_KEY
  aggregatorCA:
    crt: op://Infrastructure/talos/CLUSTER_AGGREGATORCA_CRT
    key: op://Infrastructure/talos/CLUSTER_AGGREGATORCA_KEY
  serviceAccount:
    key: op://Infrastructure/talos/CLUSTER_SERVICEACCOUNT_KEY
    {% endif %}
  {% if machinetype == 'controlplane' %}
  apiServer:
    image: registry.k8s.io/kube-apiserver:v1.33.4
    extraArgs:
      enable-aggregator-routing: true
      feature-gates: MutatingAdmissionPolicy=true
      runtime-config: admissionregistration.k8s.io/v1alpha1=true
    certSANs: ["127.0.0.1", "***************"]
    disablePodSecurityPolicy: true
    auditPolicy:
      apiVersion: audit.k8s.io/v1
      kind: Policy
      rules:
        - level: Metadata
  controllerManager:
    image: registry.k8s.io/kube-controller-manager:v1.33.4
    extraArgs:
      bind-address: 0.0.0.0
  proxy:
    disabled: true
    image: registry.k8s.io/kube-proxy:v1.33.4
  scheduler:
    image: registry.k8s.io/kube-scheduler:v1.33.4
    extraArgs:
      bind-address: 0.0.0.0
    config:
      apiVersion: kubescheduler.config.k8s.io/v1
      kind: KubeSchedulerConfiguration
      profiles:
        - schedulerName: default-scheduler
          plugins:
            score:
              disabled:
                - name: ImageLocality
          pluginConfig:
            - name: PodTopologySpread
              args:
                defaultingType: List
                defaultConstraints:
                  - maxSkew: 1
                    topologyKey: kubernetes.io/hostname
                    whenUnsatisfiable: ScheduleAnyway
  etcd:
    ca:
      crt: op://Infrastructure/talos/CLUSTER_ETCD_CA_CRT
      key: op://Infrastructure/talos/CLUSTER_ETCD_CA_KEY
    extraArgs:
      listen-metrics-urls: http://0.0.0.0:2381
    advertisedSubnets: ["*************/22"]
  allowSchedulingOnControlPlanes: true
  {% endif %}
  discovery:
    enabled: true
    registries:
      kubernetes: {}
      service:
        disabled: true
