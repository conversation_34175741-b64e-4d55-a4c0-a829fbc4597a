---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/keda.sh/scaledobject_v1alpha1.json
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: ${APP}
spec:
  advanced:
    restoreToOriginalReplicaCount: true
  cooldownPeriod: 0
  minReplicaCount: 0
  maxReplicaCount: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${APP}
  triggers:
    - type: prometheus
      metadata:
        serverAddress: http://prometheus-operated.observability.svc.cluster.local:9090
        query: probe_success{instance=~".+:2049"}
        threshold: "1"
        ignoreNullValues: "0"
