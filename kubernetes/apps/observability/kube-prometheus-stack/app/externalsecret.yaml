---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: alertmanager
spec:
  refreshInterval: 5m
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: alertmanager-secret
    template:
      data:
        ALERTMANAGER_HEARTBEAT_URL: "{{ .ALERTMANAGER_HEARTBEAT_URL }}"
        ALERTMANAGER_PUSHOVER_TOKEN: "{{ .ALERTMANAGER_PUSHOVER_TOKEN }}"
        PUSHOVER_USER_KEY: "{{ .PUSHOVER_USER_KEY }}"
  dataFrom:
    - extract:
        key: pushover
    - extract:
        key: alertmanager
