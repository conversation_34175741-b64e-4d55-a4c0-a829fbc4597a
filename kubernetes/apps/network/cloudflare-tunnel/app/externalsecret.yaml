---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: cloudflare-tunnel
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: cloudflare-tunnel-secret
    template:
      data:
        TUNNEL_TOKEN: |
          {{ toJson (dict "a" .CLOUDFLARE_ACCOUNT_TAG "t" .CLOUDFLARE_TUNNEL_ID "s" .CLOUDFLARE_TUNNEL_SECRET) | b64enc }}
  dataFrom:
    - extract:
        key: cloudflare
