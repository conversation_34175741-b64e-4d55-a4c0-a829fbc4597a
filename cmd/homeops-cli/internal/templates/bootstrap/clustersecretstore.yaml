---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/external-secrets.io/clustersecretstore_v1.json
apiVersion: external-secrets.io/v1
kind: ClusterSecretStore
metadata:
  name: onepassword
spec:
  provider:
    onepassword:
      connectHost: https://passwords.op://Infrastructure/cluster-config/SECRET_DOMAIN

      vaults:
        Infrastructure: 1
      auth:
        secretRef:
          connectTokenSecretRef:
            key: token
            name: onepassword-secret
            namespace: external-secrets